annotation.processing.enabled=true
annotation.processing.enabled.in.editor=false
annotation.processing.processors.list=
annotation.processing.run.all.processors=true
annotation.processing.source.output=${build.generated.sources.dir}/ap-source-output
application.title=Sencha Cmd
application.vendor=kkrohe
build.classes.dir=${build.dir}/classes
build.classes.excludes=**/*.java,**/*.form
# This directory is removed when the project is cleaned:
build.dir=build
build.generated.dir=${build.dir}/generated
build.generated.sources.dir=${build.dir}/generated-sources
# Only compile against the classpath explicitly listed here:
build.sysclasspath=ignore
build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results
# Uncomment to specify the preferred debugger connection transport:
#debug.transport=dt_socket
debug.classpath=${run.classpath}
debug.test.classpath=${run.test.classpath}
# Files in build.classes.dir which should be excluded from distribution jar
dist.archive.excludes=
# This directory is removed when the project is cleaned:
dist.dir=dist/SenchaCmd
dist.jar=${dist.dir}/sencha.jar
dist.javadoc.dir=${dist.dir}/javadoc
endorsed.classpath=
excludes=
includes=**
jar.compress=false
javac.classpath=${ivy.classpath}
# Space-separated list of extra javac options
javac.compilerargs=
javac.deprecation=false
javac.processorpath=${javac.classpath}
javac.source=17
javac.target=17
junit.classpath=../lib/junit-4.11.jar\:../lib/hamcrest-core-1.3.jar
javac.test.classpath=${javac.classpath}\:${junit.classpath}\:${build.classes.dir}
javac.test.processorpath=${javac.test.classpath}
javadoc.additionalparam=
javadoc.author=false
javadoc.encoding=${source.encoding}
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.private=false
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
main.class=com.sencha.command.Sencha
manifest.file=manifest.mf
meta.inf.dir=${src.dir}/META-INF
mkdist.disabled=false
platform.active=default_platform
run.classpath=${javac.classpath}\:${build.classes.dir}
# Space-separated list of JVM arguments used when running the project.
# You may also define separate properties like run-sys-prop.name=value instead of -Dname=value.
# To set system properties for unit tests define test-sys-prop.name=value:
run.test.classpath=${dist.jar}\:${junit.classpath}\:${build.test.classes.dir}
source.encoding=UTF-8
src.dir=src
test.src.dir=test
test.run.args=-Xms256m -Xmx1024m
run.jvmargs=-Xms256m -Xmx1024m
junit.forkmode=once
libs.CopyLibs.classpath=../lib/org-netbeans-modules-java-j2seproject-copylibstask.jar


ivy.classpath=lib/annotations-2.0.3.jar\:lib/ant-1.8.4.jar\:lib/ant-contrib-1.0b3.jar\:lib/ant-launcher-1.8.4.jar\:lib/bcprov-jdk15on-1.56.jar\:lib/closure-compiler-externs-v20240317.jar\:lib/closure-compiler-v20240317.jar\:lib/commons-codec-1.17.0.jar\:lib/commons-collections4-4.5.0-M2-javadoc.jar\:lib/commons-collections4-4.5.0-M2-sources.jar\:lib/commons-collections4-4.5.0-M2.jar\:lib/commons-io-2.18.0.jar\:lib/commons-lang3-3.17.0.jar\:lib/doxi.jar\:lib/error_prone_annotations-2.27.0.jar\:lib/graal-sdk-21.2.0.jar\:lib/gson-2.11.0.jar\:lib/httpclient5-5.4.1.jar\:lib/httpcore5-5.3.1.jar\:lib/httpcore5-h2-5.3.1.jar\:lib/icu4j-53.1.jar\:lib/icu4j-69.1.jar\:lib/jakarta.servlet-api-6.0.0.jar\:lib/jansi-1.9.jar\:lib/jaxb-api-2.3.0.jar\:lib/jetty-alpn-client-12.0.15.jar\:lib/jetty-client-12.0.15.jar\:lib/jetty-ee-12.0.15.jar\:lib/jetty-ee10-servlet-12.0.15.jar\:lib/jetty-ee10-webapp-12.0.15.jar\:lib/jetty-http-12.0.15.jar\:lib/jetty-io-12.0.15.jar\:lib/jetty-proxy-12.0.15.jar\:lib/jetty-security-12.0.15.jar\:lib/jetty-server-12.0.15.jar\:lib/jetty-session-12.0.15.jar\:lib/jetty-util-12.0.15.jar\:lib/jetty-xml-12.0.15.jar\:lib/jna-5.12.1.jar\:lib/js-21.2.0-javadoc.jar\:lib/js-21.2.0-sources.jar\:lib/js-21.2.0.jar\:lib/js-scriptengine-21.2.0-javadoc.jar\:lib/js-scriptengine-21.2.0-sources.jar\:lib/js-scriptengine-21.2.0.jar\:lib/phloc-commons-4.3.2.jar\:lib/phloc-css-3.7.5.jar\:lib/regex-21.2.0.jar\:lib/rhino-1.7R4.jar\:lib/slf4j-api-1.6.6.jar\:lib/slf4j-api-2.0.16.jar\:lib/slf4j-jdk14-1.6.6.jar\:lib/truffle-api-21.2.0.jar\:lib/update-1.1.0.jar\:lib/velocity-engine-core-2.4.1.jar\:lib/xercesImpl-2.12.2.jar\:lib/xml-apis-1.4.01.jar

