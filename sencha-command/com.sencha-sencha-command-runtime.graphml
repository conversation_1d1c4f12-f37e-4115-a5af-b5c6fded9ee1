<?xml version="1.0" encoding="UTF-8"?><graphml xsi:schemaLocation="http://graphml.graphdrawing.org/xmlns/graphml http://www.yworks.com/xml/schema/graphml/1.0/ygraphml.xsd" xmlns="http://graphml.graphdrawing.org/xmlns/graphml" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:y="http://www.yworks.com/xml/graphml"><key id="d0" for="node" yfiles.type="nodegraphics"/><key id="d1" for="edge" yfiles.type="edgegraphics"/><graph id="G" edgedefault="directed"><node id="com.sencha-sencha-command"><data key="d0"><y:ShapeNode><y:Fill color="#CCCCFF" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">sencha-command</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="javax.xml.bind-jaxb-api"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jaxb-api
2.3.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.bouncycastle-bcprov-jdk15on"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">bcprov-jdk15on
1.56</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.apache.httpcomponents.client5-httpclient5"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">httpclient5
5.4.1</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.apache.httpcomponents.core5-httpcore5-h2"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">httpcore5-h2
5.3.1</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.apache.httpcomponents.core5-httpcore5"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">httpcore5
5.3.1</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="net.java.dev.jna-jna"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jna
5.12.1</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.phloc-phloc-css"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">phloc-css
3.7.5</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.phloc-phloc-commons"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">phloc-commons
4.3.2</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.google.code.findbugs-annotations"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">annotations
2.0.3</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty-jetty-proxy"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-proxy
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty.ee10-jetty-ee10-webapp"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-ee10-webapp
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty.ee10-jetty-ee10-servlet"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-ee10-servlet
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty-jetty-client"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-client
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty-jetty-alpn-client"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-alpn-client
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty-jetty-security"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-security
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="jakarta.servlet-jakarta.servlet-api"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jakarta.servlet-api
6.0.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty-jetty-session"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-session
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty-jetty-ee"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-ee
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty-jetty-server"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-server
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty-jetty-xml"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-xml
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty-jetty-http"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-http
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty-jetty-io"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-io
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.eclipse.jetty-jetty-util"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jetty-util
12.0.15</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.slf4j-slf4j-api"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">slf4j-api
2.0.16
1.7.36 (evicted)
1.6.6 (evicted)
1.7.5 (evicted)</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.google.javascript-closure-compiler-externs"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">closure-compiler-externs
v20240317</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.google.javascript-closure-compiler"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">closure-compiler
v20240317</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.fusesource.jansi-jansi"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">jansi
1.9</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="commons-io-commons-io"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">commons-io
2.18.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="ant-contrib-ant-contrib"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">ant-contrib
1.0b3</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="ant-ant"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">ant
1.5</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.sencha.tools.external-yuicompressor"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">yuicompressor
2.4.7</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.apache.velocity-velocity-engine-core"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">velocity-engine-core
2.4.1</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.apache.commons-commons-lang3"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">commons-lang3
3.17.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.google.code.gson-gson"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">gson
2.11.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.google.errorprone-error_prone_annotations"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">error_prone_annotations
2.27.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.slf4j-slf4j-jdk14"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">slf4j-jdk14
1.6.6</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.apache.ant-ant"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">ant
1.8.4</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.apache.ant-ant-launcher"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">ant-launcher
1.8.4</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="xerces-xercesImpl"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">xercesImpl
2.12.2</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="xml-apis-xml-apis"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">xml-apis
1.4.01</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.mozilla-rhino"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">rhino
1.7R4</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.graalvm.js-js-scriptengine"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">js-scriptengine
21.2.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.graalvm.js-js"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">js
21.2.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.ibm.icu-icu4j"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">icu4j
69.1</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.graalvm.regex-regex"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">regex
21.2.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.graalvm.truffle-truffle-api"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">truffle-api
21.2.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.graalvm.sdk-graal-sdk"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">graal-sdk
21.2.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.apache.commons-commons-collections4"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">commons-collections4
4.5.0-M2</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="commons-codec-commons-codec"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">commons-codec
1.17.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.sencha.licenses-update"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">update
1.1.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><edge id="com.sencha-sencha-command-javax.xml.bind-jaxb-api" source="com.sencha-sencha-command" target="javax.xml.bind-jaxb-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.3.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.bouncycastle-bcprov-jdk15on" source="com.sencha-sencha-command" target="org.bouncycastle-bcprov-jdk15on"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.56</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.apache.httpcomponents.client5-httpclient5" source="com.sencha-sencha-command" target="org.apache.httpcomponents.client5-httpclient5"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">5.4.1</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.apache.httpcomponents.client5-httpclient5-org.apache.httpcomponents.core5-httpcore5-h2" source="org.apache.httpcomponents.client5-httpclient5" target="org.apache.httpcomponents.core5-httpcore5-h2"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">5.3.1</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.apache.httpcomponents.client5-httpclient5-org.apache.httpcomponents.core5-httpcore5" source="org.apache.httpcomponents.client5-httpclient5" target="org.apache.httpcomponents.core5-httpcore5"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">5.3.1</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.apache.httpcomponents.core5-httpcore5-h2-org.apache.httpcomponents.core5-httpcore5" source="org.apache.httpcomponents.core5-httpcore5-h2" target="org.apache.httpcomponents.core5-httpcore5"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">5.3.1</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-net.java.dev.jna-jna" source="com.sencha-sencha-command" target="net.java.dev.jna-jna"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">5.12.1</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-com.phloc-phloc-css" source="com.sencha-sencha-command" target="com.phloc-phloc-css"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">3.7.5</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.phloc-phloc-css-com.phloc-phloc-commons" source="com.phloc-phloc-css" target="com.phloc-phloc-commons"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">4.3.2</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.phloc-phloc-commons-com.google.code.findbugs-annotations" source="com.phloc-phloc-commons" target="com.google.code.findbugs-annotations"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.3</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.eclipse.jetty-jetty-proxy" source="com.sencha-sencha-command" target="org.eclipse.jetty-jetty-proxy"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.eclipse.jetty.ee10-jetty-ee10-webapp" source="com.sencha-sencha-command" target="org.eclipse.jetty.ee10-jetty-ee10-webapp"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty.ee10-jetty-ee10-webapp-org.eclipse.jetty.ee10-jetty-ee10-servlet" source="org.eclipse.jetty.ee10-jetty-ee10-webapp" target="org.eclipse.jetty.ee10-jetty-ee10-servlet"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.eclipse.jetty.ee10-jetty-ee10-servlet" source="com.sencha-sencha-command" target="org.eclipse.jetty.ee10-jetty-ee10-servlet"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.eclipse.jetty-jetty-client" source="com.sencha-sencha-command" target="org.eclipse.jetty-jetty-client"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-proxy-org.eclipse.jetty-jetty-client" source="org.eclipse.jetty-jetty-proxy" target="org.eclipse.jetty-jetty-client"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-client-org.eclipse.jetty-jetty-alpn-client" source="org.eclipse.jetty-jetty-client" target="org.eclipse.jetty-jetty-alpn-client"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty.ee10-jetty-ee10-servlet-org.eclipse.jetty-jetty-security" source="org.eclipse.jetty.ee10-jetty-ee10-servlet" target="org.eclipse.jetty-jetty-security"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty.ee10-jetty-ee10-servlet-jakarta.servlet-jakarta.servlet-api" source="org.eclipse.jetty.ee10-jetty-ee10-servlet" target="jakarta.servlet-jakarta.servlet-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">6.0.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty.ee10-jetty-ee10-webapp-org.eclipse.jetty-jetty-session" source="org.eclipse.jetty.ee10-jetty-ee10-webapp" target="org.eclipse.jetty-jetty-session"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty.ee10-jetty-ee10-servlet-org.eclipse.jetty-jetty-session" source="org.eclipse.jetty.ee10-jetty-ee10-servlet" target="org.eclipse.jetty-jetty-session"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty.ee10-jetty-ee10-webapp-org.eclipse.jetty-jetty-ee" source="org.eclipse.jetty.ee10-jetty-ee10-webapp" target="org.eclipse.jetty-jetty-ee"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-session-org.eclipse.jetty-jetty-server" source="org.eclipse.jetty-jetty-session" target="org.eclipse.jetty-jetty-server"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.eclipse.jetty-jetty-server" source="com.sencha-sencha-command" target="org.eclipse.jetty-jetty-server"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-ee-org.eclipse.jetty-jetty-server" source="org.eclipse.jetty-jetty-ee" target="org.eclipse.jetty-jetty-server"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-proxy-org.eclipse.jetty-jetty-server" source="org.eclipse.jetty-jetty-proxy" target="org.eclipse.jetty-jetty-server"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-security-org.eclipse.jetty-jetty-server" source="org.eclipse.jetty-jetty-security" target="org.eclipse.jetty-jetty-server"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty.ee10-jetty-ee10-servlet-org.eclipse.jetty-jetty-server" source="org.eclipse.jetty.ee10-jetty-ee10-servlet" target="org.eclipse.jetty-jetty-server"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty.ee10-jetty-ee10-webapp-org.eclipse.jetty-jetty-xml" source="org.eclipse.jetty.ee10-jetty-ee10-webapp" target="org.eclipse.jetty-jetty-xml"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-server-org.eclipse.jetty-jetty-http" source="org.eclipse.jetty-jetty-server" target="org.eclipse.jetty-jetty-http"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-client-org.eclipse.jetty-jetty-http" source="org.eclipse.jetty-jetty-client" target="org.eclipse.jetty-jetty-http"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-http-org.eclipse.jetty-jetty-io" source="org.eclipse.jetty-jetty-http" target="org.eclipse.jetty-jetty-io"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-alpn-client-org.eclipse.jetty-jetty-io" source="org.eclipse.jetty-jetty-alpn-client" target="org.eclipse.jetty-jetty-io"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-server-org.eclipse.jetty-jetty-io" source="org.eclipse.jetty-jetty-server" target="org.eclipse.jetty-jetty-io"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-client-org.eclipse.jetty-jetty-io" source="org.eclipse.jetty-jetty-client" target="org.eclipse.jetty-jetty-io"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-http-org.eclipse.jetty-jetty-util" source="org.eclipse.jetty-jetty-http" target="org.eclipse.jetty-jetty-util"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-io-org.eclipse.jetty-jetty-util" source="org.eclipse.jetty-jetty-io" target="org.eclipse.jetty-jetty-util"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-xml-org.eclipse.jetty-jetty-util" source="org.eclipse.jetty-jetty-xml" target="org.eclipse.jetty-jetty-util"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">12.0.15</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-http-org.slf4j-slf4j-api" source="org.eclipse.jetty-jetty-http" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.apache.velocity-velocity-engine-core-org.slf4j-slf4j-api" source="org.apache.velocity-velocity-engine-core" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.7.36</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.slf4j-slf4j-jdk14-org.slf4j-slf4j-api" source="org.slf4j-slf4j-jdk14" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.6.6</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-server-org.slf4j-slf4j-api" source="org.eclipse.jetty-jetty-server" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-ee-org.slf4j-slf4j-api" source="org.eclipse.jetty-jetty-ee" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-client-org.slf4j-slf4j-api" source="org.eclipse.jetty-jetty-client" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-security-org.slf4j-slf4j-api" source="org.eclipse.jetty-jetty-security" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty.ee10-jetty-ee10-servlet-org.slf4j-slf4j-api" source="org.eclipse.jetty.ee10-jetty-ee10-servlet" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.apache.httpcomponents.client5-httpclient5-org.slf4j-slf4j-api" source="org.apache.httpcomponents.client5-httpclient5" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.7.36</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-session-org.slf4j-slf4j-api" source="org.eclipse.jetty-jetty-session" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty.ee10-jetty-ee10-webapp-org.slf4j-slf4j-api" source="org.eclipse.jetty.ee10-jetty-ee10-webapp" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-io-org.slf4j-slf4j-api" source="org.eclipse.jetty-jetty-io" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.phloc-phloc-commons-org.slf4j-slf4j-api" source="com.phloc-phloc-commons" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.7.5</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.slf4j-slf4j-api" source="com.sencha-sencha-command" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-alpn-client-org.slf4j-slf4j-api" source="org.eclipse.jetty-jetty-alpn-client" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-xml-org.slf4j-slf4j-api" source="org.eclipse.jetty-jetty-xml" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-proxy-org.slf4j-slf4j-api" source="org.eclipse.jetty-jetty-proxy" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.eclipse.jetty-jetty-util-org.slf4j-slf4j-api" source="org.eclipse.jetty-jetty-util" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.0.16</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-com.google.javascript-closure-compiler-externs" source="com.sencha-sencha-command" target="com.google.javascript-closure-compiler-externs"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">v20240317</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-com.google.javascript-closure-compiler" source="com.sencha-sencha-command" target="com.google.javascript-closure-compiler"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">v20240317</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.fusesource.jansi-jansi" source="com.sencha-sencha-command" target="org.fusesource.jansi-jansi"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.9</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-commons-io-commons-io" source="com.sencha-sencha-command" target="commons-io-commons-io"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.18.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-ant-contrib-ant-contrib" source="com.sencha-sencha-command" target="ant-contrib-ant-contrib"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.0b3</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="ant-contrib-ant-contrib-ant-ant" source="ant-contrib-ant-contrib" target="ant-ant"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.5</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-com.sencha.tools.external-yuicompressor" source="com.sencha-sencha-command" target="com.sencha.tools.external-yuicompressor"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.4.7</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.apache.velocity-velocity-engine-core" source="com.sencha-sencha-command" target="org.apache.velocity-velocity-engine-core"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.4.1</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.apache.velocity-velocity-engine-core-org.apache.commons-commons-lang3" source="org.apache.velocity-velocity-engine-core" target="org.apache.commons-commons-lang3"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">3.17.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-com.google.code.gson-gson" source="com.sencha-sencha-command" target="com.google.code.gson-gson"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.11.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.google.code.gson-gson-com.google.errorprone-error_prone_annotations" source="com.google.code.gson-gson" target="com.google.errorprone-error_prone_annotations"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.27.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.slf4j-slf4j-jdk14" source="com.sencha-sencha-command" target="org.slf4j-slf4j-jdk14"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.6.6</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.apache.ant-ant" source="com.sencha-sencha-command" target="org.apache.ant-ant"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.8.4</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.apache.ant-ant-org.apache.ant-ant-launcher" source="org.apache.ant-ant" target="org.apache.ant-ant-launcher"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.8.4</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-xerces-xercesImpl" source="com.sencha-sencha-command" target="xerces-xercesImpl"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">2.12.2</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="xerces-xercesImpl-xml-apis-xml-apis" source="xerces-xercesImpl" target="xml-apis-xml-apis"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.4.01</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.mozilla-rhino" source="com.sencha-sencha-command" target="org.mozilla-rhino"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.7R4</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.graalvm.js-js-scriptengine" source="com.sencha-sencha-command" target="org.graalvm.js-js-scriptengine"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.graalvm.js-js" source="com.sencha-sencha-command" target="org.graalvm.js-js"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.js-js-com.ibm.icu-icu4j" source="org.graalvm.js-js" target="com.ibm.icu-icu4j"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">69.1</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.js-js-org.graalvm.regex-regex" source="org.graalvm.js-js" target="org.graalvm.regex-regex"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.js-js-org.graalvm.truffle-truffle-api" source="org.graalvm.js-js" target="org.graalvm.truffle-truffle-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.regex-regex-org.graalvm.truffle-truffle-api" source="org.graalvm.regex-regex" target="org.graalvm.truffle-truffle-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.js-js-org.graalvm.sdk-graal-sdk" source="org.graalvm.js-js" target="org.graalvm.sdk-graal-sdk"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.js-js-scriptengine-org.graalvm.sdk-graal-sdk" source="org.graalvm.js-js-scriptengine" target="org.graalvm.sdk-graal-sdk"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.truffle-truffle-api-org.graalvm.sdk-graal-sdk" source="org.graalvm.truffle-truffle-api" target="org.graalvm.sdk-graal-sdk"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.apache.commons-commons-collections4" source="com.sencha-sencha-command" target="org.apache.commons-commons-collections4"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">4.5.0-M2</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.apache.commons-commons-collections4-commons-codec-commons-codec" source="org.apache.commons-commons-collections4" target="commons-codec-commons-codec"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.17.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-com.sencha.licenses-update" source="com.sencha-sencha-command" target="com.sencha.licenses-update"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.1.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge></graph></graphml>