<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Ivy report :: sencha-command by com.sencha :: compile</title>
<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
<meta http-equiv="content-language" content="en">
<meta name="robots" content="index,follow">
<link rel="stylesheet" type="text/css" href="ivy-report.css">
</head>
<body>
<div id="logo">
<a href="http://ant.apache.org/ivy/"><img src="http://ant.apache.org/ivy/images/logo.png"></a>
</div>
<h1>
<a name="com.sencha-sencha-command"></a><span id="module">sencha-command <EMAIL></span> 
        by 
        <span id="organisation">com.sencha</span>
</h1>
<div id="date">
    resolved on 
      2025-06-16 20:00:50</div>
<ul id="confmenu">
<li>
<a class="active" href="com.sencha-sencha-command-compile.html">compile</a>
</li>
<li>
<a href="com.sencha-sencha-command-runtime.html">runtime</a>
</li>
<li>
<a href="com.sencha-sencha-command-test.html">test</a>
</li>
</ul>
<div id="content">
<h2>Dependencies Stats</h2>
<table class="header">
<tr>
<td class="title">Modules</td><td class="value">10</td>
</tr>
<tr>
<td class="title">Revisions</td><td class="value">10  
            (0 searched <img src="http://ant.apache.org/ivy/images/searched.gif" alt="searched" title="module revisions which required a search with a dependency resolver to be resolved">,
            0 downloaded <img src="http://ant.apache.org/ivy/images/downloaded.gif" alt="downloaded" title="module revisions for which ivy file was downloaded by dependency resolver">,
            0 evicted <img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="module revisions which were evicted by others">,
            0 errors <img src="http://ant.apache.org/ivy/images/error.gif" alt="error" title="module revisions on which error occurred">)</td>
</tr>
<tr>
<td class="title">Artifacts</td><td class="value">16 
            (0 downloaded,
            0 failed)</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">63759 kB
            (0 kB downloaded,
            63759 kB in cache)</td>
</tr>
</table>
<h2>Dependencies Overview</h2>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.graalvm.js-js-scriptengine"> js-scriptengine
         by
         org.graalvm.js</a></td><td><a href="#org.graalvm.js-js-scriptengine-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">105 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">--- graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.js-js"> js
         by
         org.graalvm.js</a></td><td><a href="#org.graalvm.js-js-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span><span style="padding-right:3px;"><a href="http://opensource.org/licenses/MIT">MIT License</a></span></td><td align="center">29846 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#com.ibm.icu-icu4j">--- icu4j
         by
         com.ibm.icu</a></td><td><a href="#com.ibm.icu-icu4j-69.1">69.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="https://raw.githubusercontent.com/unicode-org/icu/master/icu4c/LICENSE">Unicode/ICU License</a></span></td><td align="center">13044 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.regex-regex">--- regex
         by
         org.graalvm.regex</a></td><td><a href="#org.graalvm.regex-regex-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">2758 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.truffle-truffle-api">------ truffle-api
         by
         org.graalvm.truffle</a></td><td><a href="#org.graalvm.truffle-truffle-api-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">8730 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">--------- graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.truffle-truffle-api">--- truffle-api
         by
         org.graalvm.truffle</a></td><td><a href="#org.graalvm.truffle-truffle-api-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">8730 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">------ graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">--- graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.apache.commons-commons-collections4"> commons-collections4
         by
         org.apache.commons</a></td><td><a href="#org.apache.commons-commons-collections4-4.5.0-M2">4.5.0-M2</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">6580 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#commons-codec-commons-codec">--- commons-codec
         by
         commons-codec</a></td><td><a href="#commons-codec-commons-codec-1.17.0">1.17.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">364 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#com.sencha.licenses-update"> update
         by
         com.sencha.licenses</a></td><td><a href="#com.sencha.licenses-update-1.1.0">1.1.0</a></td><td align="center">release</td><td align="center">artifactory</td><td align="center">false</td><td align="center"></td><td align="center">1717 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.6.6">1.6.6</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">26 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h2>Details</h2>
<h3>
<a name="org.graalvm.js-js-scriptengine"></a>js-scriptengine by org.graalvm.js</h3>
<h4>
<a name="org.graalvm.js-js-scriptengine-21.2.0"></a>
           Revision: 21.2.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.graalvm.org/">http://www.graalvm.org/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210720165548</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">javadoc, default, system, compile, sources, provided, runtime, *, optional, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">105 kB
            (0 kB downloaded,
            105 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile</td><td>21.2.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk"> graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>js-scriptengine</td><td>source</td><td>jar</td><td align="center">no</td><td align="center">13 kB</td>
</tr>
<tr>
<td>js-scriptengine</td><td>javadoc</td><td>jar</td><td align="center">no</td><td align="center">25 kB</td>
</tr>
<tr>
<td>js-scriptengine</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">67 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.graalvm.js-js"></a>js by org.graalvm.js</h3>
<h4>
<a name="org.graalvm.js-js-21.2.0"></a>
           Revision: 21.2.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.graalvm.org/">http://www.graalvm.org/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210720165611</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">javadoc, default, system, compile, sources, provided, runtime, *, optional, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">29846 kB
            (0 kB downloaded,
            29846 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span><span style="padding-right:3px;"><a href="http://opensource.org/licenses/MIT">MIT License</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile</td><td>21.2.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#com.ibm.icu-icu4j"> icu4j
         by
         com.ibm.icu</a></td><td><a href="#com.ibm.icu-icu4j-69.1">69.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="https://raw.githubusercontent.com/unicode-org/icu/master/icu4c/LICENSE">Unicode/ICU License</a></span></td><td align="center">13044 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.regex-regex"> regex
         by
         org.graalvm.regex</a></td><td><a href="#org.graalvm.regex-regex-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">2758 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.truffle-truffle-api">--- truffle-api
         by
         org.graalvm.truffle</a></td><td><a href="#org.graalvm.truffle-truffle-api-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">8730 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">------ graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.truffle-truffle-api"> truffle-api
         by
         org.graalvm.truffle</a></td><td><a href="#org.graalvm.truffle-truffle-api-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">8730 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">--- graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk"> graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>js</td><td>javadoc</td><td>jar</td><td align="center">no</td><td align="center">8023 kB</td>
</tr>
<tr>
<td>js</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">18332 kB</td>
</tr>
<tr>
<td>js</td><td>source</td><td>jar</td><td align="center">no</td><td align="center">3491 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="com.ibm.icu-icu4j"></a>icu4j by com.ibm.icu</h3>
<h4>
<a name="com.ibm.icu-icu4j-69.1"></a>
           Revision: 69.1<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://icu-project.org/">http://icu-project.org/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210408042118</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">13044 kB
            (0 kB downloaded,
            13044 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="https://raw.githubusercontent.com/unicode-org/icu/master/icu4c/LICENSE">Unicode/ICU License</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.graalvm.js</td><td><a href="#org.graalvm.js-js">js</a></td><td>21.2.0</td><td>default, compile, runtime, master</td><td>69.1</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>icu4j</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">13044 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.graalvm.regex-regex"></a>regex by org.graalvm.regex</h3>
<h4>
<a name="org.graalvm.regex-regex-21.2.0"></a>
           Revision: 21.2.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.graalvm.org/">http://www.graalvm.org/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210720165902</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">2758 kB
            (0 kB downloaded,
            2758 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.graalvm.js</td><td><a href="#org.graalvm.js-js">js</a></td><td>21.2.0</td><td>default, compile, runtime, master</td><td>21.2.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.graalvm.truffle-truffle-api"> truffle-api
         by
         org.graalvm.truffle</a></td><td><a href="#org.graalvm.truffle-truffle-api-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">8730 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">--- graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>regex</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">2758 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.graalvm.truffle-truffle-api"></a>truffle-api by org.graalvm.truffle</h3>
<h4>
<a name="org.graalvm.truffle-truffle-api-21.2.0"></a>
           Revision: 21.2.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://openjdk.java.net/projects/graal">http://openjdk.java.net/projects/graal</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210720170148</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">8730 kB
            (0 kB downloaded,
            8730 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.graalvm.js</td><td><a href="#org.graalvm.js-js">js</a></td><td>21.2.0</td><td>default, compile, runtime, master</td><td>21.2.0</td>
</tr>
<tr>
<td>org.graalvm.regex</td><td><a href="#org.graalvm.regex-regex">regex</a></td><td>21.2.0</td><td>compile, runtime</td><td>21.2.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk"> graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>truffle-api</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">8730 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.graalvm.sdk-graal-sdk"></a>graal-sdk by org.graalvm.sdk</h3>
<h4>
<a name="org.graalvm.sdk-graal-sdk-21.2.0"></a>
           Revision: 21.2.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://github.com/oracle/graal">https://github.com/oracle/graal</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210720165914</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">590 kB
            (0 kB downloaded,
            590 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.graalvm.js</td><td><a href="#org.graalvm.js-js">js</a></td><td>21.2.0</td><td>default, compile, runtime, master</td><td>21.2.0</td>
</tr>
<tr>
<td>org.graalvm.js</td><td><a href="#org.graalvm.js-js-scriptengine">js-scriptengine</a></td><td>21.2.0</td><td>default, compile, runtime, master</td><td>21.2.0</td>
</tr>
<tr>
<td>org.graalvm.truffle</td><td><a href="#org.graalvm.truffle-truffle-api">truffle-api</a></td><td>21.2.0</td><td>compile, runtime</td><td>21.2.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>graal-sdk</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">590 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.apache.commons-commons-collections4"></a>commons-collections4 by org.apache.commons</h3>
<h4>
<a name="org.apache.commons-commons-collections4-4.5.0-M2"></a>
           Revision: 4.5.0-M2<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://commons.apache.org/proper/commons-collections/">https://commons.apache.org/proper/commons-collections/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20240615075904</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">javadoc, default, system, compile, sources, provided, runtime, *, optional, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">6580 kB
            (0 kB downloaded,
            6580 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile</td><td>4.5.0-M2</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#commons-codec-commons-codec"> commons-codec
         by
         commons-codec</a></td><td><a href="#commons-codec-commons-codec-1.17.0">1.17.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">364 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>commons-collections4</td><td>javadoc</td><td>jar</td><td align="center">no</td><td align="center">4995 kB</td>
</tr>
<tr>
<td>commons-collections4</td><td>source</td><td>jar</td><td align="center">no</td><td align="center">754 kB</td>
</tr>
<tr>
<td>commons-collections4</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">831 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="commons-codec-commons-codec"></a>commons-codec by commons-codec</h3>
<h4>
<a name="commons-codec-commons-codec-1.17.0"></a>
           Revision: 1.17.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://commons.apache.org/proper/commons-codec/">https://commons.apache.org/proper/commons-codec/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20240420234153</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">364 kB
            (0 kB downloaded,
            364 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.apache.commons</td><td><a href="#org.apache.commons-commons-collections4">commons-collections4</a></td><td>4.5.0-M2</td><td>optional</td><td>1.17.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>commons-codec</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">364 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="com.sencha.licenses-update"></a>update by com.sencha.licenses</h3>
<h4>
<a name="com.sencha.licenses-update-1.1.0"></a>
           Revision: 1.1.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20250527142254</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">artifactory</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">1717 kB
            (0 kB downloaded,
            1717 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile</td><td>1.1.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>update</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">1717 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.slf4j-slf4j-api"></a>slf4j-api by org.slf4j</h3>
<h4>
<a name="org.slf4j-slf4j-api-1.6.6"></a>
           Revision: 1.6.6<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.slf4j.org">http://www.slf4j.org</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20120611201148</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">26 kB
            (0 kB downloaded,
            26 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile</td><td>1.6.6</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>slf4j-api</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">26 kB</td>
</tr>
</tbody>
</table>
</div>
</body>
</html>
