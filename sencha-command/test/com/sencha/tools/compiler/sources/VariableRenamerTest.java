/*
 * Copyright (c) 2012-2016. Sencha Inc.
 */

package com.sencha.tools.compiler.sources;

import com.sencha.tools.compiler.ScriptTestBase;
import com.sencha.tools.compiler.ast.AstUtil;
import com.sencha.tools.compiler.ast.SourceBuilder;
import com.sencha.tools.compiler.ast.js.RootNode;
import com.sencha.tools.compressors.es6.VariableRenamer;
import com.sencha.util.FileUtil;
import com.sencha.util.RegexUtil;
import com.sencha.util.StringUtil;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;

import java.io.File;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.Assert.assertEquals;

public class VariableRenamerTest extends ScriptTestBase {
    
    
    @BeforeClass
    public static void beforeClass() {
        SourceBuilder.WrapLines = 100;
    }
    
    @AfterClass 
    public static void afterClass() {
        SourceBuilder.WrapLines = 0;
    }
    
    @Test
    public void testUnderscore() {
        String _inputFile = "underscore_min.js";
        String _expectedFile = "underscore_min_expected.js";
        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));
        String inputContent = FileUtil.readFile(inputFile);
        RootNode node = AstUtil.parseClosure(inputContent);
        VariableRenamer renamer = new VariableRenamer();
        renamer.setKeepFunctionNames(false);
        renamer.compress(node);
        String content = AstUtil.toSource(node);
        Pattern p = RegexUtil.getInstance().get("^\\s+?$", Pattern.MULTILINE);
        Matcher m = p.matcher(content);
        content = m.replaceAll("");
        String expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");
        assertEquals(expected, content);

        _expectedFile = "underscore_min_expected_minified.js";
        expectedFile = new File(getPath(_expectedFile, 2));
        expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");

        content = AstUtil.toSource(node, false);
        m = p.matcher(content);
        content = m.replaceAll("");

        assertEquals(StringUtil.normalizeLineEndings(expected), 
            StringUtil.normalizeLineEndings(content));
    }  
    
    @Test
    public void tesetCKEditor() {
        String _inputFile = "ckeditor.js";
        String _expectedFile = "ckeditor_expected.js";
        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));
        String inputContent = FileUtil.readFile(inputFile);
        RootNode node = AstUtil.parseClosure(inputContent);
        VariableRenamer renamer = new VariableRenamer();
        renamer.setKeepFunctionNames(false);
        renamer.compress(node);
        String content = AstUtil.toSource(node);
        Pattern p = RegexUtil.getInstance().get("^\\s+?$", Pattern.MULTILINE);
        Matcher m = p.matcher(content);
        content = m.replaceAll("");
        String expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");
        assertEquals(expected, content);

        _expectedFile = "ckeditor_expected_minified.js";
        expectedFile = new File(getPath(_expectedFile, 2));
        expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");

        content = AstUtil.toSource(node, false);
        m = p.matcher(content);
        content = m.replaceAll("");

        assertEquals(StringUtil.normalizeLineEndings(expected), 
            StringUtil.normalizeLineEndings(content));
    }  
    
    // @Test - Disabled due to destructuring assignment parsing issues
    public void testVariableRenamer() {
        String _inputFile = "VariableRenamer_Input.js";
        String _expectedFile = "VariableRenamer_Expected.js";
        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));
        String inputContent = FileUtil.readFile(inputFile);
        RootNode node = AstUtil.parseClosure(inputContent);
        VariableRenamer renamer = new VariableRenamer();
        renamer.setKeepFunctionNames(false);
        renamer.compress(node);
        String content = AstUtil.toSource(node);
        Pattern p = RegexUtil.getInstance().get("^\\s+?$", Pattern.MULTILINE);
        Matcher m = p.matcher(content);
        content = m.replaceAll("");
        String expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");
        assertEquals(expected, content);

        _expectedFile = "VariableRenamer_Expected_Minified.js";
        expectedFile = new File(getPath(_expectedFile, 2));
        expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");

        content = AstUtil.toSource(node, false);
        m = p.matcher(content);
        content = m.replaceAll("");

        assertEquals(StringUtil.normalizeLineEndings(expected), 
            StringUtil.normalizeLineEndings(content));
    }  
    
    @Test
    public void testVariableRenamer2() {
        String _inputFile = "VariableRenamer_Input2.js";
        String _expectedFile = "VariableRenamer_Expected2.js";
        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));
        String inputContent = FileUtil.readFile(inputFile);
        RootNode node = AstUtil.parseClosure(inputContent);
        VariableRenamer renamer = new VariableRenamer();
        renamer.setKeepFunctionNames(false);
        renamer.compress(node);
        String content = AstUtil.toSource(node);
        Pattern p = RegexUtil.getInstance().get("^\\s+?$", Pattern.MULTILINE);
        Matcher m = p.matcher(content);
        content = m.replaceAll("");
        String expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");
        assertEquals(expected, content);

        _expectedFile = "VariableRenamer_Expected_Minified2.js";
        expectedFile = new File(getPath(_expectedFile, 2));
        expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");

        content = AstUtil.toSource(node, false);
        m = p.matcher(content);
        content = m.replaceAll("");

        assertEquals(StringUtil.normalizeLineEndings(expected), 
            StringUtil.normalizeLineEndings(content));
    }  
    
    @Test
    public void testVariableRenamerMinifiedInput() {
        String _inputFile = "VariableRenamer_Min_Input.js";
        String _expectedFile = "VariableRenamer_Min_Expected.js";
        File inputFile = new File(getPath(_inputFile, 2));
        File expectedFile = new File(getPath(_expectedFile, 2));
        String inputContent = FileUtil.readFile(inputFile);
        RootNode node = AstUtil.parseClosure(inputContent);
        VariableRenamer renamer = new VariableRenamer();
        renamer.setKeepFunctionNames(false);
        renamer.compress(node);
        String content = AstUtil.toSource(node);
        Pattern p = RegexUtil.getInstance().get("^\\s+?$", Pattern.MULTILINE);
        Matcher m = p.matcher(content);
        content = m.replaceAll("");
        String expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");
        assertEquals(expected, content);

        _expectedFile = "VariableRenamer_Min_Expected_Minified.js";
        expectedFile = new File(getPath(_expectedFile, 2));
        expected = FileUtil.readFile(expectedFile);
        m = p.matcher(expected);
        expected = m.replaceAll("");

        content = AstUtil.toSource(node, false);
        m = p.matcher(content);
        content = m.replaceAll("");

        assertEquals(StringUtil.normalizeLineEndings(expected),
            StringUtil.normalizeLineEndings(content));
    }


}
