// http://es6-features.org/#ValueExportImport
import * as math from "lib/math";
console.log("2π = " + math.sum(math.pi, math.pi));
import { sum, pi } from "lib/math";
console.log("2π = " + sum(pi, pi));
import exp, { pi, e } from "lib/mathplusplus";
console.log("e^{π} = " + exp(pi));
export function sum(x, y) {
    return x + y;
};
export var pi = 3.141593;
export var π = pi;
class MyClass extends foo {
    constructor() {
        super();
    }
    get myProp () {
        return this._myProp;
    }
    set myProp (value) {
        this._myProp = value;
    }
    static method() {}
    static get Property () {
        return MyClass.prototype.prop;
    }
    static set Property (val) {
        MyClass.prototype.prop = val;
    }
    anotherMethod() {
        return {
            property: value,
            get foo () {
                return this.foo;
            },
            set foo (value) {
                this.foo = value;
            },
            method() {
                return (foo, bar) => {
                    console.log(foo, bar);
                };
            },
            arrowFunctions: function() {
                var odds = evens.map(v => v + 1),
                    pairs = evens.map(v => ({
                        even: v,
                        odd: v + 1
                    })),
                    nums = evens.map((v, i) => v + i);
            },
            asyncAwait: function() {
                read();
                async function read() {
                    var html = await getRandomPonyFooArticle();
                    var md = hget(html, {
                            markdown: true,
                            root: 'main',
                            ignore: '.at-subscribe,.mm-comments,.de-sidebar'
                        });
                    var txt = marked(md, {
                            renderer: new Term()
                        });
                    console.log(txt);
                }
                const fn1 = async path => {};
                const fn0 = async (path, name) => {};
                const fn1 = async path => {};
                const fn2 = async () => {};
                async function fn3(path) {}

                const o = {
                        async fn4() {}
                    };
            },
            stringEscapes: function() {
                var stringEscapes = {
                        '\\': '\\',
                        "'": "'",
                        '\n': 'n',
                        '\r': 'r',
                        ' ': 'u2028',
                        ' ': 'u2029'
                    };
            },
            templateStrings: function() {
                var customer = {
                        name: "Foo"
                    };
                var card = {
                        amount: 7,
                        product: "Bar",
                        unitprice: 42
                    };
                var message = `Hello ${customer.name},
                want to buy ${card.amount} ${card.product} for
                a total of ${card.amount * card.unitprice} bucks?`;
                get`http://example.com/foo?bar=${bar + baz}&quux=${quux}`;
                console.log(`I'm a template literal!`);
                function quux(strings, ...values) {
                    strings[0] === "foo\n";
                    strings[1] === "bar";
                    strings.raw[0] === "foo\\n";
                    strings.raw[1] === "bar";
                    values[0] === 42;
                }
                quux`foo\n${42}bar`;
                String.raw`foo\n${42}bar` === "foo\\n42bar";
            },
            literals: function() {
                503 === 503;
                503 === 503;
                1.15292150460684698E18 === 1.15292150460684698E18;
                0x10000000000000000 === 0x10000000000000000;
            },
            propertyNames: function() {
                var obj = {
                        x,
                        y
                    };
                let obj2 = {
                        foo: "bar",
                        ["baz" + quux()]: 42
                    };
                obj2 = {
                    foo(a, b) {},
                    bar(x, y) {},
                    * quux(x, y) {}
                };
            },
            parameterHandling: function() {
                function f(x, y = 7, z = 42) {
                    return x + y + z;
                }
                if (f(1) !== 50) {
                    throw "doesn't work";
                }
                function f(x, y, ...a) {
                    return (x + y) * a.length;
                }
                f(1, 2, "hello", true, 7) === 9;
                var params = [
                        "hello",
                        true,
                        7
                    ];
                var other = [
                        1,
                        2,
                        ...params
                    ];
                f(1, 2, ...params) === 9;
                var str = "foo";
                var chars = [
                        ...str
                    ];
            },
            iterators: function() {
                let fibonacci = {
                        [Symbol.iterator]() {
                            let pre = 0,
                                cur = 1;
                            return {
                                next() {
                                    [ pre, cur ] = [
                                        cur,
                                        pre + cur
                                    ];
                                    return {
                                        done: false,
                                        value: cur
                                    };
                                }
                            };
                        }
                    };
                for (let n of fibonacci) {
                    if (n > 1000)  {
                        break;
                    }

                    console.log(n);
                }
            },
            generators: function() {
                let fibonacci = {
                        * [Symbol.iterator]() {
                            let pre = 0,
                                cur = 1;
                            for (; ; ) {
                                [ pre, cur ] = [
                                    cur,
                                    pre + cur
                                ];
                                yield cur;
                            }
                        }
                    };
                for (let n of fibonacci) {
                    if (n > 1000)  {
                        break;
                    }

                    console.log(n);
                }
                function * range(start, end, step) {
                    while (start < end) {
                        yield start;
                        start += step;
                    }
                }
                for (let i of range(0, 10, 2)) {
                    console.log(i);
                }
                let fibonacci2 = function *(numbers) {
                        let pre = 0,
                            cur = 1;
                        while (numbers-- > 0) {
                            [ pre, cur ] = [
                                cur,
                                pre + cur
                            ];
                            yield cur;
                        }
                    };
                for (let n of fibonacci(1000)) console.log(n);
                let numbers = [
                        ...fibonacci(1000)
                    ];
                let [ n1, n2, n3, ...others ] = fibonacci(1000);
                class Clz {
                    * bar() {}

                }

                let Obj = {
                        * foo() {}
                    };
            },
            symbols: function() {
                Symbol.for("app.foo") === Symbol.for("app.foo");
                const foo = Symbol.for("app.foo");
                const bar = Symbol.for("app.bar");
                Symbol.keyFor(foo) === "app.foo";
                Symbol.keyFor(bar) === "app.bar";
                typeof foo === "symbol";
                typeof bar === "symbol";
                let obj = {};
                obj[foo] = "foo";
                obj[bar] = "bar";
                JSON.stringify(obj);
                Object.keys(obj);
                Object.getOwnPropertyNames(obj);
                Object.getOwnPropertySymbols(obj);
            },
            destructuringAssignment: function() {
                var list = [
                        1,
                        2,
                        3
                    ];
                var [ a, , b ] = list;
                [ b, a ] = [
                    a,
                    b
                ];
                var { op, lhs, rhs } = getASTNode();
                var { op: a, lhs: { op: b }, rhs: c } = getASTNode();
                function f([ name, val ]) {
                    console.log(name, val);
                }
                function g({ name: n, val: v }) {
                    console.log(n, v);
                }
                function h({ name, val }) {
                    console.log(name, val);
                }
                f([
                    "bar",
                    42
                ]);
                g({
                    name: "foo",
                    val: 7
                });
                h({
                    name: "bar",
                    val: 42
                });
                var list = [
                        7,
                        42
                    ];
                var [ a = 1, b = 2, c = 3, d ] = list;
                a === 7;
                b === 42;
                c === 3;
                d === undefined;
            }
        };
    }

}
