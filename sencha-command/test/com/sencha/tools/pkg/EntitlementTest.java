/*
 * Copyright (c) 2012-2015. Sencha Inc.
 */

package com.sencha.tools.pkg;

import com.sencha.command.Sencha;
import com.sencha.logging.SenchaLogManager;
import com.sencha.util.FileUtil;
import com.sencha.util.Locator;
import com.sencha.util.PathUtil;
import com.sencha.util.URLUtil;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;

import java.io.File;
import java.net.URL;
import java.util.logging.Level;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class EntitlementTest extends BaseRepositoryTest {
    private static final Logger _logger = SenchaLogManager.getLogger();
    
    @Before
    public void before() {
        FileUtil.delete(_repoDir1);
        FileUtil.delete(_repoDir2);
        SenchaLogManager.setLogLevel(Level.INFO);
    }

    @After
    public void after() {
        FileUtil.delete(_repoDir1);
        FileUtil.delete(_repoDir2);
        SenchaLogManager.setLogLevel(null);
    }

    @Test
    public void testEntitlements() {
        String testTempDir = getTestTempDir().getAbsolutePath();
        if(PathUtil.exists(testTempDir)) {
            FileUtil.delete(testTempDir);
        }

        LocalRepository repo = createRepo(new File(_repoDir1));
        repo.init("testUser");
        repo.getRemoteManager().deleteRemote("sencha");

        LocalRepository repo2 = createRepo(new File(_repoDir2));
        repo2.init("testUser2");
        repo2.getRemoteManager().deleteRemote("sencha");

        
        String pkgPath = PathUtil.join(Locator.getTestDir(), "package/locker-test.pkg");
        String realPkgPath = PathUtil.join(testTempDir, "package/locker-test-actual.pkg");
        PathUtil.ensurePathExists(realPkgPath);
        FileUtil.copy(pkgPath, realPkgPath);
        String extractPath = PathUtil.join(testTempDir, "extract-test/locker-test");

        File pkgFile =  new File(realPkgPath);
        File extractDir = new File(extractPath);

        if(extractDir.exists()) {
            FileUtil.delete(extractDir);
        }
        
        repo.addPackage(pkgFile);
        Model.Package pkg = repo.getPackage("locker-test");
        repo.extractPackage(pkg, extractDir, "windows");
        String readmePath = PathUtil.join(extractPath, "src", "Readme.md");
        String content = FileUtil.readFile(readmePath);
        assertFalse(content.contains("commercial"));
        
        
        Sencha sencha = new Sencha();
        sencha.setRepositoryDir(new File(_repoDir1));
        sencha.getRepo(true);
        sencha.dispatch(new String[]{
            "package",
            "grant",
            "-package=locker-test",
            "-locker=commercial",
            "-user=testUser"
        });
        
        FileUtil.delete(extractDir);
        repo.extractPackage(pkg, extractDir, "windows");
        content = FileUtil.readFile(readmePath);
        assertTrue(content.contains("commercial"));
        
        File repoPkgDir = repo.getBaseDir().getParentFile();
        URL url = getWebRoot(repoPkgDir);
        _logger.info("Serving dir {} as {}", repoPkgDir, url);
        repo.getRemoteManager().addRemote("repo2", URLUtil.toURL(url.toString() + "repo2/pkgs"));
        repo2.getRemoteManager().addRemote("repo1", URLUtil.toURL(url.toString() + "repo1/pkgs"));

        repo.getRemoteManager().each(new RepositoryRemoteManager.EachRemote() {
            @Override
            public boolean onRemote(RemoteRepository remote) {
                remote.sync();
                return true;
            }
        });
        
        repo2.getRemoteManager().each(new RepositoryRemoteManager.EachRemote() {
            @Override
            public boolean onRemote(RemoteRepository remote) {
                remote.sync();
                return true;
            }
        });

        Model.Package pkg2 = repo2.getPackage("locker-test");
        repo2.syncPackage(pkg2, "windows");
        FileUtil.delete(extractDir);
        repo2.extractPackage(pkg2, extractDir, "windows");
        content = FileUtil.readFile(readmePath);
        assertFalse(content.contains("commercial"));


        sencha.dispatch(new String[]{
            "package",
            "grant",
            "-package=locker-test",
            "-locker=commercial",
            "-user=testUser2"
        });
        
        pkg2 = repo2.getPackage("locker-test");
        repo2.syncPackage(pkg2, "windows");
        try {
            Thread.sleep(1000);
        } catch (InterruptedException ex) {
            java.util.logging.Logger.getLogger(EntitlementTest.class.getName()).log(Level.SEVERE, null, ex);
        }
        FileUtil.delete(extractDir);
        repo2.extractPackage(pkg2, extractDir, "windows");
        content = FileUtil.readFile(readmePath);
        assertTrue(content.contains("commercial"));
    }



    private String _repoDir1 = PathUtil.join(getTestTempDir().getAbsolutePath(), "tempRepo", "repo1");
    private String _repoDir2 = PathUtil.join(getTestTempDir().getAbsolutePath(), "tempRepo", "repo2");
}
