<?xml version="1.0" encoding="UTF-8"?>
<!-- You may freely edit this file. See commented blocks below for -->
<!-- some examples of how to customize the build. -->
<!-- (If you delete it and reopen the project it will be recreated.) -->
<!-- By default, only the Clean and Build commands use this build script. -->
<!-- Commands such as Run, Debug, and Test only use this build script if -->
<!-- the Compile on Save feature is turned off for the project. -->
<!-- You can turn off the Compile on Save (or Deploy on Save) setting -->
<!-- in the project's Project Properties dialog box.-->
<project name="sencha-command" default="default" basedir="." xmlns:ivy="antlib:org.apache.ivy.ant" xmlns:artifact="antlib:org.apache.maven.artifact.ant">
    <description>Builds, tests, and runs the project sencha-command.</description>
    <import file="nbproject/build-impl.xml"/>
    <!--

    There exist several targets which are by default empty and which can be 
    used for execution of your tasks. These targets are usually executed 
    before and after some main targets. They are: 

      -pre-init:                 called before initialization of project properties
      -post-init:                called after initialization of project properties
      -pre-compile:              called before javac compilation
      -post-compile:             called after javac compilation
      -pre-compile-single:       called before javac compilation of single file
      -post-compile-single:      called after javac compilation of single file
      -pre-compile-test:         called before javac compilation of JUnit tests
      -post-compile-test:        called after javac compilation of JUnit tests
      -pre-compile-test-single:  called before javac compilation of single JUnit test
      -post-compile-test-single: called after javac compilation of single JUunit test
      -pre-jar:                  called before JAR building
      -post-jar:                 called after JAR building
      -post-clean:               called after cleaning build products

    (Targets beginning with '-' are not intended to be called on their own.)

    Example of inserting an obfuscator after compilation could look like this:

        <target name="-post-compile">
            <obfuscate>
                <fileset dir="${build.classes.dir}"/>
            </obfuscate>
        </target>

    For list of available properties check the imported 
    nbproject/build-impl.xml file. 


    Another way to customize the build is by overriding existing main targets.
    The targets of interest are: 

      -init-macrodef-javac:     defines macro for javac compilation
      -init-macrodef-junit:     defines macro for junit execution
      -init-macrodef-debug:     defines macro for class debugging
      -init-macrodef-java:      defines macro for class execution
      -do-jar:                  JAR building
      run:                      execution of project 
      -javadoc-build:           Javadoc generation
      test-report:              JUnit report generation

    An example of overriding the target for project execution could look like this:

        <target name="run" depends="sencha-command-impl.jar">
            <exec dir="bin" executable="launcher.exe">
                <arg file="${dist.jar}"/>
            </exec>
        </target>

    Notice that the overridden target depends on the jar target and not only on 
    the compile target as the regular run target does. Again, for a list of available 
    properties which you can use, check the target you are overriding in the
    nbproject/build-impl.xml file. 

    -->

    <path id="maven-ant-tasks.classpath" path="${basedir}/../lib/maven-ant-tasks-2.1.3.jar" />
    <typedef resource="org/apache/maven/artifact/ant/antlib.xml"
             uri="antlib:org.apache.maven.artifact.ant"
             classpathref="maven-ant-tasks.classpath" />
    
    <target name="detect-doxi">
        <condition property="doxi.available" value="true">
            <available file="${basedir}/../modules/doxi" type="dir"/>
        </condition>
    </target>
    
    <target name="build-doxi" depends="detect-doxi" if="doxi.available" unless="skip.doxi">
        <echo>Building Doxi</echo>
        <!--
            MAVEN-ANT-TASKS attempts to use a super POM that has non-HTTPS maven repo URLs
            that throw 501 errors. Maven >= 3.2 requires HTTPS only (Jan 15., 2020)
            As a workaround, we build with java directly and still use the maven-ant-task
            plugin to resolve dependencies. - J.N.
        -->
        <exec executable="mvn" dir="${basedir}/../modules/doxi">
            <arg value="package"/>
        </exec>
        <artifact:dependencies filesetId="doxi.deps" useScope="runtime">
            <pom file="${basedir}/../modules/doxi/pom.xml"/>
        </artifact:dependencies>
        <echo>Dependencies: ${toString:doxi.deps}</echo>
        <echo>Lib dir: ${basedir}/lib</echo>
        <copy todir="${basedir}/lib">
            <fileset dir="${basedir}/../modules/doxi/target/" includes="*.jar"/>
        </copy>
        <copy todir="${basedir}/lib" verbose="true">
            <fileset refid="doxi.deps" />
            <mapper type="flatten"/>
        </copy>
    </target>

    <target name="-init-ivy">
        <path id="ivy.lib.path">
            <fileset dir="${basedir}/../lib/" includes="ivy-2.3.0.jar"/>
        </path>
        <taskdef resource="org/apache/ivy/ant/antlib.xml"
                 uri="antlib:org.apache.ivy.ant"
                 classpathref="ivy.lib.path"/>
        <ivy:settings file="${basedir}/../ivysettings.xml"/>
    </target>
    
    <target name="-ivy-retrieve" depends="-init-ivy">
        <local name="property.file"/>
        <property name="property.file" value="${basedir}/nbproject/project.properties"/>
        <ivy:retrieve/> <!-- Load dependencies to the project -->
        <pathconvert property="ivy.classpath.computed" dirsep="/" pathsep=":">
            <path>
                <fileset dir="lib">
                    <include name="*.jar"/>
                    <exclude name="yui*.jar"/>
                    <exclude name="js-1.7R2.jar"/>
                </fileset>
            </path>
            <map from="${basedir}${file.separator}" to=""/>
        </pathconvert>

        <propertyfile file="${property.file}">
            <entry operation="=" key="ivy.classpath" value="${ivy.classpath.computed}"/>
        </propertyfile>
        <copy file="${property.file}" tofile="${property.file}.tmp">
            <filterchain>
                <headfilter lines="-1" skip="1"/>
            </filterchain>
        </copy>
        <move file="${property.file}.tmp" tofile="${property.file}"/>
    </target>

    <target name="ivy-report" depends="-ivy-retrieve">
        <ivy:report/>
    </target>
    
    <target name="-pre-compile" depends="build-doxi,-ivy-retrieve"/>
    <target name="-pre-compile-single" depends="build-doxi,-ivy-retrieve"/>
    <target name="-post-clean">
        <delete dir="lib"/>
        <delete dir="${dist.dir}"/>
    </target>

    <macrodef name="concat-ant-files">
        <attribute name="target"/>
        <attribute name="src"/>
        <sequential>

            <concat destfile="@{target}/include-ant-task.js">
                <fileset file="@{src}/ant-util.js"/>
                <fileset file="@{src}/tasks/include.js"/>
            </concat>

            <concat destfile="@{target}/git-changelog-ant-task.js">
                <fileset file="@{src}/ant-util.js"/>
                <fileset file="@{src}/tasks/git-changelog.js"/>
            </concat>


            <concat destfile="@{target}/jsduck-ant-task.js">
                <fileset file="@{src}/ant-util.js"/>
                <fileset file="@{src}/tasks/jsduck.js"/>
            </concat>

            <concat destfile="@{target}/escape-ant-task.js">
                <fileset file="@{src}/ant-util.js"/>
                <fileset file="@{src}/tasks/escape.js"/>
            </concat>

            <concat destfile="@{target}/jira-query-ant-task.js">
                <fileset file="@{src}/ant-util.js"/>
                <fileset file="@{src}/tasks/jira-query.js"/>
            </concat>

            <concat destfile="@{target}/make-url-ant-task.js">
                <fileset file="@{src}/ant-util.js"/>
                <fileset file="@{src}/tasks/make-url.js"/>
            </concat>

        </sequential>
    </macrodef>

    <target name="-setup-dist-folder">
        <copy todir="${dist.dir}">
            <fileset dir="${basedir}/files" includes="**/*"/>
        </copy>
        <copy todir="${dist.dir}/lib">
            <fileset dir="${basedir}/lib">
                <include name="*.jar"/>
            </fileset>
        </copy>

        <delete>
            <fileset dir="${dist.dir}/lib">
                <include name="*-sources.jar"/>
                <include name="*-javadoc.jar"/>
                <include name="js-1.7R2.jar"/>
            </fileset>
        </delete>
        
        <copy todir="${dist.dir}/extensions/cmd-packager">
            <fileset dir="${basedir}/../cmd-packager"
                     includes="**/*"
                     excludes="*.iml"/>
        </copy>

        <concat-ant-files src="${dist.dir}/ant" target="${dist.dir}/ant"/>
        
        <mkdir dir="${dist.dir}/ant/build/app"/>
        <mkdir dir="${dist.dir}/ant/build/package"/>
        
        <copy todir="${dist.dir}/ant/build/app" overwrite="true">
            <fileset dir="${basedir}/files/templates/SenchaCommon">
                <include name="**/*"/>
                <exclude name="*sencha.cfg*"/>
                <exclude name="*find-cmd*"/>
            </fileset>
            <fileset dir="${basedir}/files/templates/app/{senchadir}/app">
                <include name="**/*"/>
                <exclude name="*sencha.cfg*"/>
                <exclude name="*find-cmd*"/>
            </fileset>
            <fileset dir="${basedir}/files/plugins/ext/current/templates/App/.sencha/app">
                <exclude name="*sencha.cfg*"/>
                <exclude name="*find-cmd*"/>
            </fileset>
            <fileset dir="${basedir}/files/plugins/touch/2.3/templates/App/.sencha/app">
                <include name="microloader/**/*"/>
                <exclude name="*sencha.cfg*"/>
                <exclude name="*find-cmd*"/>
            </fileset>
            <chainedmapper>
                <regexpmapper from="^(.*?)(\.default)?$$" to="\1"/>
                <regexpmapper from="^(.*?)(\.merge)?$$" to="\1"/>
            </chainedmapper>
        </copy> 
        <copy todir="${dist.dir}/ant/build/app" overwrite="true">
            <fileset dir="${basedir}/files/ant/build/app">
                <include name="**/*"/>
                <exclude name="*sencha.cfg*"/>
                <exclude name="*find-cmd*"/>
            </fileset>
            <chainedmapper>
                <regexpmapper from="^(.*?)(\.default)?$$" to="\1"/>
                <regexpmapper from="^(.*?)(\.merge)?$$" to="\1"/>
            </chainedmapper>
        </copy> 
        
        <copy todir="${dist.dir}/ant/build/package" overwrite="true">
            <fileset dir="${basedir}/files/templates/SenchaCommon">
                <include name="**/*"/>
                <exclude name="*sencha.cfg*"/>
                <exclude name="*find-cmd*"/>
            </fileset>
            <fileset dir="${basedir}/files/templates/package/{senchadir}/package">
                <include name="**/*"/>
                <exclude name="*sencha.cfg*"/>
                <exclude name="*find-cmd*"/>
                <exclude name="*plugin.xml.default*"/>
            </fileset>
            <chainedmapper>
                <regexpmapper from="^(.*?)(\.default)?$$" to="\1"/>
                <regexpmapper from="^(.*?)(\.merge)?$$" to="\1"/>
            </chainedmapper>
        </copy> 
        <copy todir="${dist.dir}/ant/build/package" overwrite="true">
            <fileset dir="${basedir}/files/ant/build/package">
                <include name="**/*"/>
                <exclude name="*sencha.cfg*"/>
                <exclude name="*find-cmd*"/>
            </fileset>
            <chainedmapper>
                <regexpmapper from="^(.*?)(\.default)?$$" to="\1"/>
                <regexpmapper from="^(.*?)(\.merge)?$$" to="\1"/>
            </chainedmapper>
        </copy>

        <copy todir="${dist.dir}/js/node_modules/fashion">
            <fileset dir="${basedir}/../modules/fashion">
                <include name="lib/**/*"/>
                <include name="src/**/*"/>
                <include name="test/**/*"/>
                <include name="dist/**/*"/>
                <include name="*.js"/>
                <include name="*.json"/>
                <include name="node_modules/**/*"/>
            </fileset>
        </copy>

        <!-- pass -Dnpm.executable=yarn to use yarn instead -->
        <condition property="use.vm.launcher" value="false">
            <os family="windows"/>
        </condition>
        <property name="use.vm.launcher" value="true"/>
        <property name="npm.executable" value="npm" />
        <exec executable="${npm.executable}"
              dir="${dist.dir}/js/node_modules/ext-precache"
              failonerror="true"
              failifexecutionfails="true"
              vmlauncher="${use.vm.launcher}">
            <arg value="install"></arg>
            <arg value="--only=prod"></arg>
        </exec>
        <!--
        <exec executable="${npm.executable}"
              dir="${dist.dir}/js/node_modules/fashion"
              failonerror="true"
              failifexecutionfails="true"
              vmlauncher="${use.vm.launcher}">
            <arg value="install"></arg>
            <arg value="##only=prod"></arg>
        </exec>
        -->
    </target>

    <target name="-setup-working-dir">
        <path id="graalvm.classpath">
            <fileset dir="${basedir}/lib">
                <include name="**/*.jar"/>
            </fileset>
        </path>

        <property name="script.classpath" refid="graalvm.classpath"/>
        <property name="javax.script.engine" value="graal.js"/>
        <property name="work.dir.value" value="${build.dir}/test-temp"/>

        <script language="javascript" classpathref="graalvm.classpath"><![CDATA[
            project.setProperty("work.dir", project.getProperty("work.dir.value"));
        ]]></script>
        <mkdir dir="${work.dir}"/>
    </target>

    <target name="-post-jar" depends="-setup-dist-folder,-setup-working-dir">
        <java dir="${dist.dir}" jar="${dist.dir}/sencha.jar" fork="true" failonerror="true">
            <arg value="help"/>
            <arg value="-r"/>
            <arg value="-m"/>
            <arg value="-o"/>
            <arg value="${basedir}/docs/guides/command_reference/README.md"/>
        </java>
    </target>
    
    <target name="-pre-test-run-single" 
            depends="-setup-dist-folder,sencha-command-impl.-pre-test-run-single"/>

    <target name="-pre-test-run" 
            depends="-setup-dist-folder,sencha-command-impl.-pre-test-run"/>

    <target name="-pre-test" depends="-setup-dist-folder"/>

    <target name="external-test-setup" 
            depends="jar,compile-test" 
            description="setup project for external testing (IDEA)"/>
    
    <target name="external-test-run"
            depends="external-test-setup,test"/>
</project>
