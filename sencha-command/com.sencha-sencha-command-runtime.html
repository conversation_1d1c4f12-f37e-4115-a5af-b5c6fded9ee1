<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Ivy report :: sencha-command by com.sencha :: runtime</title>
<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
<meta http-equiv="content-language" content="en">
<meta name="robots" content="index,follow">
<link rel="stylesheet" type="text/css" href="ivy-report.css">
</head>
<body>
<div id="logo">
<a href="http://ant.apache.org/ivy/"><img src="http://ant.apache.org/ivy/images/logo.png"></a>
</div>
<h1>
<a name="com.sencha-sencha-command"></a><span id="module">sencha-command <EMAIL></span> 
        by 
        <span id="organisation">com.sencha</span>
</h1>
<div id="date">
    resolved on 
      2025-06-16 20:00:50</div>
<ul id="confmenu">
<li>
<a href="com.sencha-sencha-command-compile.html">compile</a>
</li>
<li>
<a class="active" href="com.sencha-sencha-command-runtime.html">runtime</a>
</li>
<li>
<a href="com.sencha-sencha-command-test.html">test</a>
</li>
</ul>
<div id="content">
<h2>Dependencies Stats</h2>
<table class="header">
<tr>
<td class="title">Modules</td><td class="value">50</td>
</tr>
<tr>
<td class="title">Revisions</td><td class="value">53  
            (3 searched <img src="http://ant.apache.org/ivy/images/searched.gif" alt="searched" title="module revisions which required a search with a dependency resolver to be resolved">,
            3 downloaded <img src="http://ant.apache.org/ivy/images/downloaded.gif" alt="downloaded" title="module revisions for which ivy file was downloaded by dependency resolver">,
            3 evicted <img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="module revisions which were evicted by others">,
            0 errors <img src="http://ant.apache.org/ivy/images/error.gif" alt="error" title="module revisions on which error occurred">)</td>
</tr>
<tr>
<td class="title">Artifacts</td><td class="value">55 
            (0 downloaded,
            0 failed)</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">98759 kB
            (0 kB downloaded,
            98759 kB in cache)</td>
</tr>
</table>
<h2>Conflicts</h2>
<table class="conflicts">
<thead>
<tr>
<th>Module</th><th>Selected</th><th>Evicted</th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.slf4j-slf4j-api">slf4j-api
             by
             org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a> </td><td><a href="#org.slf4j-slf4j-api-1.7.36">1.7.36 </a> <a href="#org.slf4j-slf4j-api-1.6.6">1.6.6 </a> <a href="#org.slf4j-slf4j-api-1.7.5">1.7.5 </a> </td>
</tr>
</tbody>
</table>
<h2>Dependencies Overview</h2>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#javax.xml.bind-jaxb-api"> jaxb-api
         by
         javax.xml.bind</a></td><td><a href="#javax.xml.bind-jaxb-api-2.3.0">2.3.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">123 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.bouncycastle-bcprov-jdk15on"> bcprov-jdk15on
         by
         org.bouncycastle</a></td><td><a href="#org.bouncycastle-bcprov-jdk15on-1.56">1.56</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.bouncycastle.org/licence.html">Bouncy Castle Licence</a></span></td><td align="center">3368 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.apache.httpcomponents.client5-httpclient5"> httpclient5
         by
         org.apache.httpcomponents.client5</a></td><td><a href="#org.apache.httpcomponents.client5-httpclient5-5.4.1">5.4.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">887 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.apache.httpcomponents.core5-httpcore5-h2">--- httpcore5-h2
         by
         org.apache.httpcomponents.core5</a></td><td><a href="#org.apache.httpcomponents.core5-httpcore5-h2-5.3.1">5.3.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">235 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.apache.httpcomponents.core5-httpcore5">------ httpcore5
         by
         org.apache.httpcomponents.core5</a></td><td><a href="#org.apache.httpcomponents.core5-httpcore5-5.3.1">5.3.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">885 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.apache.httpcomponents.core5-httpcore5">--- httpcore5
         by
         org.apache.httpcomponents.core5</a></td><td><a href="#org.apache.httpcomponents.core5-httpcore5-5.3.1">5.3.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">885 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.7.36">1.7.36</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></td>
</tr>
<tr>
<td><a href="#net.java.dev.jna-jna"> jna
         by
         net.java.dev.jna</a></td><td><a href="#net.java.dev.jna-jna-5.12.1">5.12.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="https://www.gnu.org/licenses/old-licenses/lgpl-2.1">LGPL-2.1-or-later</a></span><span style="padding-right:3px;"><a href="https://www.apache.org/licenses/LICENSE-2.0.txt">Apache-2.0</a></span></td><td align="center">1822 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#com.phloc-phloc-css"> phloc-css
         by
         com.phloc</a></td><td><a href="#com.phloc-phloc-css-3.7.5">3.7.5</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache 2</a></span></td><td align="center">471 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#com.phloc-phloc-commons">--- phloc-commons
         by
         com.phloc</a></td><td><a href="#com.phloc-phloc-commons-4.3.2">4.3.2</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache 2</a></span></td><td align="center">1702 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#com.google.code.findbugs-annotations">------ annotations
         by
         com.google.code.findbugs</a></td><td><a href="#com.google.code.findbugs-annotations-2.0.3">2.0.3</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.gnu.org/licenses/lgpl.html">GNU Lesser Public License</a></span></td><td align="center">75 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.7.5">1.7.5</a></td><td align="center"></td><td align="center"></td><td align="center"></td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-proxy"> jetty-proxy
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-proxy-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">24 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-client">--- jetty-client
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-client-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">345 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-alpn-client">------ jetty-alpn-client
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-alpn-client-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">7 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">------ jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">--- jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">------ jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty.ee10-jetty-ee10-webapp"> jetty-ee10-webapp
         by
         org.eclipse.jetty.ee10</a></td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-webapp-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">128 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty.ee10-jetty-ee10-servlet">--- jetty-ee10-servlet
         by
         org.eclipse.jetty.ee10</a></td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-servlet-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">422 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-security">------ jetty-security
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-security-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">142 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">--------- jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">------------ jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#jakarta.servlet-jakarta.servlet-api">------ jakarta.servlet-api
         by
         jakarta.servlet</a></td><td><a href="#jakarta.servlet-jakarta.servlet-api-6.0.0">6.0.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.eclipse.org/legal/epl-2.0">EPL 2.0</a></span><span style="padding-right:3px;"><a href="https://www.gnu.org/software/classpath/license.html">GPL2 w/ CPE</a></span></td><td align="center">339 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-session">------ jetty-session
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-session-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">114 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">--------- jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">------------ jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">------ jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--------- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-session">--- jetty-session
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-session-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">114 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">------ jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--------- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-ee">--- jetty-ee
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-ee-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">4 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">------ jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--------- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-xml">--- jetty-xml
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-xml-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">81 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty.ee10-jetty-ee10-servlet"> jetty-ee10-servlet
         by
         org.eclipse.jetty.ee10</a></td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-servlet-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">422 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-security">--- jetty-security
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-security-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">142 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">------ jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--------- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#jakarta.servlet-jakarta.servlet-api">--- jakarta.servlet-api
         by
         jakarta.servlet</a></td><td><a href="#jakarta.servlet-jakarta.servlet-api-6.0.0">6.0.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.eclipse.org/legal/epl-2.0">EPL 2.0</a></span><span style="padding-right:3px;"><a href="https://www.gnu.org/software/classpath/license.html">GPL2 w/ CPE</a></span></td><td align="center">339 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-session">--- jetty-session
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-session-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">114 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">------ jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--------- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">--- jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">------ jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-client"> jetty-client
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-client-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">345 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-alpn-client">--- jetty-alpn-client
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-alpn-client-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">7 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server"> jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.7.36">1.7.36</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.6.6">1.6.6</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 1.7.36"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.7.5">1.7.5</a></td><td align="center"></td><td align="center"></td><td align="center"></td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></td>
</tr>
<tr>
<td><a href="#com.google.javascript-closure-compiler-externs"> closure-compiler-externs
         by
         com.google.javascript</a></td><td><a href="#com.google.javascript-closure-compiler-externs-v20240317">v20240317</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">257 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/searched.gif" alt="searched" title="required a search in repository"><img src="http://ant.apache.org/ivy/images/downloaded.gif" alt="downloaded" title="downloaded from repository"></td>
</tr>
<tr>
<td><a href="#com.google.javascript-closure-compiler"> closure-compiler
         by
         com.google.javascript</a></td><td><a href="#com.google.javascript-closure-compiler-v20240317">v20240317</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">13657 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/searched.gif" alt="searched" title="required a search in repository"><img src="http://ant.apache.org/ivy/images/downloaded.gif" alt="downloaded" title="downloaded from repository"></td>
</tr>
<tr>
<td><a href="#org.fusesource.jansi-jansi"> jansi
         by
         org.fusesource.jansi</a></td><td><a href="#org.fusesource.jansi-jansi-1.9">1.9</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">111 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#commons-io-commons-io"> commons-io
         by
         commons-io</a></td><td><a href="#commons-io-commons-io-2.18.0">2.18.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">526 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#ant-contrib-ant-contrib"> ant-contrib
         by
         ant-contrib</a></td><td><a href="#ant-contrib-ant-contrib-1.0b3">1.0b3</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://ant-contrib.sourceforge.net/tasks/LICENSE.txt">Unknown License</a></span></td><td align="center">219 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#ant-ant">--- ant
         by
         ant</a></td><td><a href="#ant-ant-1.5">1.5</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#com.sencha.tools.external-yuicompressor"> yuicompressor
         by
         com.sencha.tools.external</a></td><td><a href="#com.sencha.tools.external-yuicompressor-2.4.7">2.4.7</a></td><td align="center">release</td><td align="center">sencha-legacy</td><td align="center">false</td><td align="center"></td><td align="center">870 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.apache.velocity-velocity-engine-core"> velocity-engine-core
         by
         org.apache.velocity</a></td><td><a href="#org.apache.velocity-velocity-engine-core-2.4.1">2.4.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">504 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.7.36">1.7.36</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></td>
</tr>
<tr>
<td><a href="#org.apache.commons-commons-lang3">--- commons-lang3
         by
         org.apache.commons</a></td><td><a href="#org.apache.commons-commons-lang3-3.17.0">3.17.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">658 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#com.google.code.gson-gson"> gson
         by
         com.google.code.gson</a></td><td><a href="#com.google.code.gson-gson-2.11.0">2.11.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="https://www.apache.org/licenses/LICENSE-2.0.txt">Apache-2.0</a></span></td><td align="center">291 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#com.google.errorprone-error_prone_annotations">--- error_prone_annotations
         by
         com.google.errorprone</a></td><td><a href="#com.google.errorprone-error_prone_annotations-2.27.0">2.27.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0.txt">Apache 2.0</a></span></td><td align="center">19 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-jdk14"> slf4j-jdk14
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-jdk14-1.6.6">1.6.6</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">9 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.7.36">1.7.36</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.6.6">1.6.6</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 1.7.36"></td>
</tr>
<tr>
<td><a href="#org.apache.ant-ant"> ant
         by
         org.apache.ant</a></td><td><a href="#org.apache.ant-ant-1.8.4">1.8.4</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">1896 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.apache.ant-ant-launcher">--- ant-launcher
         by
         org.apache.ant</a></td><td><a href="#org.apache.ant-ant-launcher-1.8.4">1.8.4</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">18 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#xerces-xercesImpl"> xercesImpl
         by
         xerces</a></td><td><a href="#xerces-xercesImpl-2.12.2">2.12.2</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0.txt">The Apache Software License, Version 2.0</a></span></td><td align="center">1412 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#xml-apis-xml-apis">--- xml-apis
         by
         xml-apis</a></td><td><a href="#xml-apis-xml-apis-1.4.01">1.4.01</a></td><td align="center">release</td><td align="center">local-chain</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0.txt">The Apache Software License, Version 2.0</a></span><span style="padding-right:3px;"><a href="http://www.saxproject.org/copying.html">The SAX License</a></span><span style="padding-right:3px;"><a href="http://www.w3.org/TR/2004/REC-DOM-Level-3-Core-20040407/java-binding.zip">The W3C License</a></span></td><td align="center">215 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.mozilla-rhino"> rhino
         by
         org.mozilla</a></td><td><a href="#org.mozilla-rhino-1.7R4">1.7R4</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.mozilla.org/MPL/2.0/index.txt">Mozilla Public License, Version 2.0</a></span></td><td align="center">1108 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/searched.gif" alt="searched" title="required a search in repository"><img src="http://ant.apache.org/ivy/images/downloaded.gif" alt="downloaded" title="downloaded from repository"></td>
</tr>
<tr>
<td><a href="#org.graalvm.js-js-scriptengine"> js-scriptengine
         by
         org.graalvm.js</a></td><td><a href="#org.graalvm.js-js-scriptengine-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">105 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">--- graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.js-js"> js
         by
         org.graalvm.js</a></td><td><a href="#org.graalvm.js-js-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span><span style="padding-right:3px;"><a href="http://opensource.org/licenses/MIT">MIT License</a></span></td><td align="center">29846 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#com.ibm.icu-icu4j">--- icu4j
         by
         com.ibm.icu</a></td><td><a href="#com.ibm.icu-icu4j-69.1">69.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="https://raw.githubusercontent.com/unicode-org/icu/master/icu4c/LICENSE">Unicode/ICU License</a></span></td><td align="center">13044 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.regex-regex">--- regex
         by
         org.graalvm.regex</a></td><td><a href="#org.graalvm.regex-regex-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">2758 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.truffle-truffle-api">------ truffle-api
         by
         org.graalvm.truffle</a></td><td><a href="#org.graalvm.truffle-truffle-api-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">8730 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">--------- graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.truffle-truffle-api">--- truffle-api
         by
         org.graalvm.truffle</a></td><td><a href="#org.graalvm.truffle-truffle-api-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">8730 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">------ graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">--- graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.apache.commons-commons-collections4"> commons-collections4
         by
         org.apache.commons</a></td><td><a href="#org.apache.commons-commons-collections4-4.5.0-M2">4.5.0-M2</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">6580 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#commons-codec-commons-codec">--- commons-codec
         by
         commons-codec</a></td><td><a href="#commons-codec-commons-codec-1.17.0">1.17.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">364 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#com.sencha.licenses-update"> update
         by
         com.sencha.licenses</a></td><td><a href="#com.sencha.licenses-update-1.1.0">1.1.0</a></td><td align="center">release</td><td align="center">artifactory</td><td align="center">false</td><td align="center"></td><td align="center">1717 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h2>Details</h2>
<h3>
<a name="javax.xml.bind-jaxb-api"></a>jaxb-api by javax.xml.bind</h3>
<h4>
<a name="javax.xml.bind-jaxb-api-2.3.0"></a>
           Revision: 2.3.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20170731165724</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">123 kB
            (0 kB downloaded,
            123 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>2.3.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jaxb-api</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">123 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.bouncycastle-bcprov-jdk15on"></a>bcprov-jdk15on by org.bouncycastle</h3>
<h4>
<a name="org.bouncycastle-bcprov-jdk15on-1.56"></a>
           Revision: 1.56<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.bouncycastle.org/java.html">http://www.bouncycastle.org/java.html</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20161223104136</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">3368 kB
            (0 kB downloaded,
            3368 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://www.bouncycastle.org/licence.html">Bouncy Castle Licence</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>1.56</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>bcprov-jdk15on</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">3368 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.apache.httpcomponents.client5-httpclient5"></a>httpclient5 by org.apache.httpcomponents.client5</h3>
<h4>
<a name="org.apache.httpcomponents.client5-httpclient5-5.4.1"></a>
           Revision: 5.4.1<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241025130055</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">887 kB
            (0 kB downloaded,
            887 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>5.4.1</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.apache.httpcomponents.core5-httpcore5-h2"> httpcore5-h2
         by
         org.apache.httpcomponents.core5</a></td><td><a href="#org.apache.httpcomponents.core5-httpcore5-h2-5.3.1">5.3.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">235 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.apache.httpcomponents.core5-httpcore5">--- httpcore5
         by
         org.apache.httpcomponents.core5</a></td><td><a href="#org.apache.httpcomponents.core5-httpcore5-5.3.1">5.3.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">885 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.apache.httpcomponents.core5-httpcore5"> httpcore5
         by
         org.apache.httpcomponents.core5</a></td><td><a href="#org.apache.httpcomponents.core5-httpcore5-5.3.1">5.3.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">885 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.7.36">1.7.36</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>httpclient5</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">887 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.apache.httpcomponents.core5-httpcore5-h2"></a>httpcore5-h2 by org.apache.httpcomponents.core5</h3>
<h4>
<a name="org.apache.httpcomponents.core5-httpcore5-h2-5.3.1"></a>
           Revision: 5.3.1<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241019155033</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">235 kB
            (0 kB downloaded,
            235 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.apache.httpcomponents.client5</td><td><a href="#org.apache.httpcomponents.client5-httpclient5">httpclient5</a></td><td>5.4.1</td><td>default, compile, runtime, master</td><td>5.3.1</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.apache.httpcomponents.core5-httpcore5"> httpcore5
         by
         org.apache.httpcomponents.core5</a></td><td><a href="#org.apache.httpcomponents.core5-httpcore5-5.3.1">5.3.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">885 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>httpcore5-h2</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">235 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.apache.httpcomponents.core5-httpcore5"></a>httpcore5 by org.apache.httpcomponents.core5</h3>
<h4>
<a name="org.apache.httpcomponents.core5-httpcore5-5.3.1"></a>
           Revision: 5.3.1<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241019155008</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">885 kB
            (0 kB downloaded,
            885 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.apache.httpcomponents.client5</td><td><a href="#org.apache.httpcomponents.client5-httpclient5">httpclient5</a></td><td>5.4.1</td><td>default, compile, runtime, master</td><td>5.3.1</td>
</tr>
<tr>
<td>org.apache.httpcomponents.core5</td><td><a href="#org.apache.httpcomponents.core5-httpcore5-h2">httpcore5-h2</a></td><td>5.3.1</td><td>compile, runtime</td><td>5.3.1</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>httpcore5</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">885 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="net.java.dev.jna-jna"></a>jna by net.java.dev.jna</h3>
<h4>
<a name="net.java.dev.jna-jna-5.12.1"></a>
           Revision: 5.12.1<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://github.com/java-native-access/jna">https://github.com/java-native-access/jna</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20220629225504</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">1822 kB
            (0 kB downloaded,
            1822 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="https://www.gnu.org/licenses/old-licenses/lgpl-2.1">LGPL-2.1-or-later</a></span><span style="padding-right:3px;"><a href="https://www.apache.org/licenses/LICENSE-2.0.txt">Apache-2.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>5.12.1</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jna</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">1822 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="com.phloc-phloc-css"></a>phloc-css by com.phloc</h3>
<h4>
<a name="com.phloc-phloc-css-3.7.5"></a>
           Revision: 3.7.5<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://repo.phloc.com/apidocs/phloc-css/${project.version}">http://repo.phloc.com/apidocs/phloc-css/${project.version}</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20140520213351</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">471 kB
            (0 kB downloaded,
            471 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache 2</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>3.7.5</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#com.phloc-phloc-commons"> phloc-commons
         by
         com.phloc</a></td><td><a href="#com.phloc-phloc-commons-4.3.2">4.3.2</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache 2</a></span></td><td align="center">1702 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#com.google.code.findbugs-annotations">--- annotations
         by
         com.google.code.findbugs</a></td><td><a href="#com.google.code.findbugs-annotations-2.0.3">2.0.3</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.gnu.org/licenses/lgpl.html">GNU Lesser Public License</a></span></td><td align="center">75 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.7.5">1.7.5</a></td><td align="center"></td><td align="center"></td><td align="center"></td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>phloc-css</td><td>bundle</td><td>jar</td><td align="center">no</td><td align="center">471 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="com.phloc-phloc-commons"></a>phloc-commons by com.phloc</h3>
<h4>
<a name="com.phloc-phloc-commons-4.3.2"></a>
           Revision: 4.3.2<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://repo.phloc.com/apidocs/phloc-commons/${project.version}">http://repo.phloc.com/apidocs/phloc-commons/${project.version}</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20140520210218</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">1702 kB
            (0 kB downloaded,
            1702 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache 2</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.phloc</td><td><a href="#com.phloc-phloc-css">phloc-css</a></td><td>3.7.5</td><td>default, compile, runtime, master</td><td>4.3.2</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#com.google.code.findbugs-annotations"> annotations
         by
         com.google.code.findbugs</a></td><td><a href="#com.google.code.findbugs-annotations-2.0.3">2.0.3</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.gnu.org/licenses/lgpl.html">GNU Lesser Public License</a></span></td><td align="center">75 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.7.5">1.7.5</a></td><td align="center"></td><td align="center"></td><td align="center"></td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>phloc-commons</td><td>bundle</td><td>jar</td><td align="center">no</td><td align="center">1702 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="com.google.code.findbugs-annotations"></a>annotations by com.google.code.findbugs</h3>
<h4>
<a name="com.google.code.findbugs-annotations-2.0.3"></a>
           Revision: 2.0.3<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://findbugs.sourceforge.net/">http://findbugs.sourceforge.net/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20131231130503</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">75 kB
            (0 kB downloaded,
            75 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://www.gnu.org/licenses/lgpl.html">GNU Lesser Public License</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.phloc</td><td><a href="#com.phloc-phloc-commons">phloc-commons</a></td><td>4.3.2</td><td>compile, runtime</td><td>2.0.3</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>annotations</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">75 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty-jetty-proxy"></a>jetty-proxy by org.eclipse.jetty</h3>
<h4>
<a name="org.eclipse.jetty-jetty-proxy-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012530</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">24 kB
            (0 kB downloaded,
            24 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty-jetty-client"> jetty-client
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-client-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">345 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-alpn-client">--- jetty-alpn-client
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-alpn-client-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">7 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server"> jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-proxy</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">24 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty.ee10-jetty-ee10-webapp"></a>jetty-ee10-webapp by org.eclipse.jetty.ee10</h3>
<h4>
<a name="org.eclipse.jetty.ee10-jetty-ee10-webapp-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012607</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">128 kB
            (0 kB downloaded,
            128 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty.ee10-jetty-ee10-servlet"> jetty-ee10-servlet
         by
         org.eclipse.jetty.ee10</a></td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-servlet-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">422 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-security">--- jetty-security
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-security-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">142 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">------ jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--------- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#jakarta.servlet-jakarta.servlet-api">--- jakarta.servlet-api
         by
         jakarta.servlet</a></td><td><a href="#jakarta.servlet-jakarta.servlet-api-6.0.0">6.0.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.eclipse.org/legal/epl-2.0">EPL 2.0</a></span><span style="padding-right:3px;"><a href="https://www.gnu.org/software/classpath/license.html">GPL2 w/ CPE</a></span></td><td align="center">339 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-session">--- jetty-session
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-session-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">114 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">------ jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--------- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">--- jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">------ jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-session"> jetty-session
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-session-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">114 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">--- jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">------ jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-ee"> jetty-ee
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-ee-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">4 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">--- jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">------ jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-xml"> jetty-xml
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-xml-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">81 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-ee10-webapp</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">128 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty.ee10-jetty-ee10-servlet"></a>jetty-ee10-servlet by org.eclipse.jetty.ee10</h3>
<h4>
<a name="org.eclipse.jetty.ee10-jetty-ee10-servlet-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012606</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">422 kB
            (0 kB downloaded,
            422 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty.ee10</td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-webapp">jetty-ee10-webapp</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty-jetty-security"> jetty-security
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-security-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">142 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">--- jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">------ jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#jakarta.servlet-jakarta.servlet-api"> jakarta.servlet-api
         by
         jakarta.servlet</a></td><td><a href="#jakarta.servlet-jakarta.servlet-api-6.0.0">6.0.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.eclipse.org/legal/epl-2.0">EPL 2.0</a></span><span style="padding-right:3px;"><a href="https://www.gnu.org/software/classpath/license.html">GPL2 w/ CPE</a></span></td><td align="center">339 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-session"> jetty-session
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-session-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">114 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server">--- jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">------ jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--------- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server"> jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-ee10-servlet</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">422 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty-jetty-client"></a>jetty-client by org.eclipse.jetty</h3>
<h4>
<a name="org.eclipse.jetty-jetty-client-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012524</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">345 kB
            (0 kB downloaded,
            345 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-proxy">jetty-proxy</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty-jetty-alpn-client"> jetty-alpn-client
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-alpn-client-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">7 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http"> jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io"> jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-client</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">345 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty-jetty-alpn-client"></a>jetty-alpn-client by org.eclipse.jetty</h3>
<h4>
<a name="org.eclipse.jetty-jetty-alpn-client-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012506</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">7 kB
            (0 kB downloaded,
            7 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-client">jetty-client</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io"> jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-alpn-client</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">7 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty-jetty-security"></a>jetty-security by org.eclipse.jetty</h3>
<h4>
<a name="org.eclipse.jetty-jetty-security-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012523</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">142 kB
            (0 kB downloaded,
            142 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty.ee10</td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-servlet">jetty-ee10-servlet</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server"> jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-security</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">142 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="jakarta.servlet-jakarta.servlet-api"></a>jakarta.servlet-api by jakarta.servlet</h3>
<h4>
<a name="jakarta.servlet-jakarta.servlet-api-6.0.0"></a>
           Revision: 6.0.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://projects.eclipse.org/projects/ee4j.servlet">https://projects.eclipse.org/projects/ee4j.servlet</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20220512124933</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">339 kB
            (0 kB downloaded,
            339 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://www.eclipse.org/legal/epl-2.0">EPL 2.0</a></span><span style="padding-right:3px;"><a href="https://www.gnu.org/software/classpath/license.html">GPL2 w/ CPE</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty.ee10</td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-servlet">jetty-ee10-servlet</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>6.0.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jakarta.servlet-api</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">339 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty-jetty-session"></a>jetty-session by org.eclipse.jetty</h3>
<h4>
<a name="org.eclipse.jetty-jetty-session-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012517</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">114 kB
            (0 kB downloaded,
            114 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty.ee10</td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-webapp">jetty-ee10-webapp</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty.ee10</td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-servlet">jetty-ee10-servlet</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server"> jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-session</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">114 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty-jetty-ee"></a>jetty-ee by org.eclipse.jetty</h3>
<h4>
<a name="org.eclipse.jetty-jetty-ee-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012528</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">4 kB
            (0 kB downloaded,
            4 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty.ee10</td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-webapp">jetty-ee10-webapp</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty-jetty-server"> jetty-server
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-server-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">612 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http">--- jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">------ jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--------- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-ee</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">4 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty-jetty-server"></a>jetty-server by org.eclipse.jetty</h3>
<h4>
<a name="org.eclipse.jetty-jetty-server-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012510</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">612 kB
            (0 kB downloaded,
            612 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-session">jetty-session</a></td><td>12.0.15</td><td>compile, runtime</td><td>12.0.15</td>
</tr>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-ee">jetty-ee</a></td><td>12.0.15</td><td>compile, runtime</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-proxy">jetty-proxy</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-security">jetty-security</a></td><td>12.0.15</td><td>compile, runtime</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty.ee10</td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-servlet">jetty-ee10-servlet</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty-jetty-http"> jetty-http
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-http-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">409 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io">--- jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">------ jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--------- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io"> jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-server</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">612 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty-jetty-xml"></a>jetty-xml by org.eclipse.jetty</h3>
<h4>
<a name="org.eclipse.jetty-jetty-xml-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012510</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">81 kB
            (0 kB downloaded,
            81 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty.ee10</td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-webapp">jetty-ee10-webapp</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util"> jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-xml</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">81 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty-jetty-http"></a>jetty-http by org.eclipse.jetty</h3>
<h4>
<a name="org.eclipse.jetty-jetty-http-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012507</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">409 kB
            (0 kB downloaded,
            409 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-server">jetty-server</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-client">jetty-client</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty-jetty-io"> jetty-io
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-io-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">319 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util">--- jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">------ slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util"> jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-http</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">409 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty-jetty-io"></a>jetty-io by org.eclipse.jetty</h3>
<h4>
<a name="org.eclipse.jetty-jetty-io-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012506</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">319 kB
            (0 kB downloaded,
            319 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-http">jetty-http</a></td><td>12.0.15</td><td>compile, runtime</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-alpn-client">jetty-alpn-client</a></td><td>12.0.15</td><td>compile, runtime</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-server">jetty-server</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-client">jetty-client</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.eclipse.jetty-jetty-util"> jetty-util
         by
         org.eclipse.jetty</a></td><td><a href="#org.eclipse.jetty-jetty-util-12.0.15">12.0.15</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">672 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api">--- slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-io</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">319 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.eclipse.jetty-jetty-util"></a>jetty-util by org.eclipse.jetty</h3>
<h4>
<a name="org.eclipse.jetty-jetty-util-12.0.15"></a>
           Revision: 12.0.15<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241106012504</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">672 kB
            (0 kB downloaded,
            672 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-http">jetty-http</a></td><td>12.0.15</td><td>compile, runtime</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-io">jetty-io</a></td><td>12.0.15</td><td>compile, runtime</td><td>12.0.15</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-xml">jetty-xml</a></td><td>12.0.15</td><td>compile, runtime</td><td>12.0.15</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jetty-util</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">672 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.slf4j-slf4j-api"></a>slf4j-api by org.slf4j</h3>
<h4>
<a name="org.slf4j-slf4j-api-2.0.16"></a>
           Revision: 2.0.16<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.slf4j.org">http://www.slf4j.org</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20240810144508</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), default, compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">68 kB
            (0 kB downloaded,
            68 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-http">jetty-http</a></td><td>12.0.15</td><td>compile, runtime</td><td>2.0.16</td>
</tr>
<tr>
<td>org.apache.velocity</td><td><a href="#org.apache.velocity-velocity-engine-core">velocity-engine-core</a></td><td>2.4.1</td><td>default, compile, runtime, master</td><td>1.7.36</td>
</tr>
<tr>
<td>org.slf4j</td><td><a href="#org.slf4j-slf4j-jdk14">slf4j-jdk14</a></td><td>1.6.6</td><td>default, compile, runtime, master</td><td>1.6.6</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-server">jetty-server</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>2.0.16</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-ee">jetty-ee</a></td><td>12.0.15</td><td>compile, runtime</td><td>2.0.16</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-client">jetty-client</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>2.0.16</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-security">jetty-security</a></td><td>12.0.15</td><td>compile, runtime</td><td>2.0.16</td>
</tr>
<tr>
<td>org.eclipse.jetty.ee10</td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-servlet">jetty-ee10-servlet</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>2.0.16</td>
</tr>
<tr>
<td>org.apache.httpcomponents.client5</td><td><a href="#org.apache.httpcomponents.client5-httpclient5">httpclient5</a></td><td>5.4.1</td><td>default, compile, runtime, master</td><td>1.7.36</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-session">jetty-session</a></td><td>12.0.15</td><td>compile, runtime</td><td>2.0.16</td>
</tr>
<tr>
<td>org.eclipse.jetty.ee10</td><td><a href="#org.eclipse.jetty.ee10-jetty-ee10-webapp">jetty-ee10-webapp</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>2.0.16</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-io">jetty-io</a></td><td>12.0.15</td><td>compile, runtime</td><td>2.0.16</td>
</tr>
<tr>
<td>com.phloc</td><td><a href="#com.phloc-phloc-commons">phloc-commons</a></td><td>4.3.2</td><td>compile, runtime</td><td>1.7.5</td>
</tr>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>2.0.16</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-alpn-client">jetty-alpn-client</a></td><td>12.0.15</td><td>compile, runtime</td><td>2.0.16</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-xml">jetty-xml</a></td><td>12.0.15</td><td>compile, runtime</td><td>2.0.16</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-proxy">jetty-proxy</a></td><td>12.0.15</td><td>default, compile, runtime, master</td><td>2.0.16</td>
</tr>
<tr>
<td>org.eclipse.jetty</td><td><a href="#org.eclipse.jetty-jetty-util">jetty-util</a></td><td>12.0.15</td><td>compile, runtime</td><td>2.0.16</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>slf4j-api</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">68 kB</td>
</tr>
</tbody>
</table>
<h4>
<a name="org.slf4j-slf4j-api-1.7.36"></a>
           Revision: 1.7.36<span style="padding-left:15px;"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.slf4j.org">http://www.slf4j.org</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20220208190351</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), default, compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">0 kB
            (0 kB downloaded,
            0 kB in cache)</td>
</tr>
<tr>
<td class="title">Evicted by</td><td class="value"><b>2.0.16 </b> <b></b>
			 in <b>latest-revision</b> conflict manager
        </td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.apache.httpcomponents.client5</td><td><a href="#org.apache.httpcomponents.client5-httpclient5">httpclient5</a></td><td>5.4.1</td><td>default, compile, runtime, master</td><td>1.7.36</td>
</tr>
<tr>
<td>org.apache.velocity</td><td><a href="#org.apache.velocity-velocity-engine-core">velocity-engine-core</a></td><td>2.4.1</td><td>default, compile, runtime, master</td><td>1.7.36</td>
</tr>
<tr>
<td>org.slf4j</td><td><a href="#org.slf4j-slf4j-jdk14">slf4j-jdk14</a></td><td>1.6.6</td><td>default, compile, runtime, master</td><td>1.6.6</td>
</tr>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>1.7.36</td>
</tr>
</tbody>
</table>
<h4>
<a name="org.slf4j-slf4j-api-1.6.6"></a>
           Revision: 1.6.6<span style="padding-left:15px;"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 1.7.36"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.slf4j.org">http://www.slf4j.org</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20120611201148</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">0 kB
            (0 kB downloaded,
            0 kB in cache)</td>
</tr>
<tr>
<td class="title">Evicted by</td><td class="value"><b>1.7.36 </b> <b></b>
			 in <b>latest-revision</b> conflict manager
        </td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.slf4j</td><td><a href="#org.slf4j-slf4j-jdk14">slf4j-jdk14</a></td><td>1.6.6</td><td>default, compile, runtime, master</td><td>1.6.6</td>
</tr>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>1.6.6</td>
</tr>
</tbody>
</table>
<h4>
<a name="org.slf4j-slf4j-api-1.7.5"></a>
           Revision: 1.7.5<span style="padding-left:15px;"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></span>
</h4>
<table class="header">
<tr>
<td class="title">Status</td><td class="value"></td>
</tr>
<tr>
<td class="title">Publication</td><td class="value"></td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value"></td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), default, compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">0 kB
            (0 kB downloaded,
            0 kB in cache)</td>
</tr>
<tr>
<td class="title">Evicted by</td><td class="value"><b>2.0.16 </b> <b></b>
			 in <b>latest-revision</b> conflict manager
        </td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.phloc</td><td><a href="#com.phloc-phloc-commons">phloc-commons</a></td><td>4.3.2</td><td>compile, runtime</td><td>1.7.5</td>
</tr>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>1.7.5</td>
</tr>
</tbody>
</table>
<h3>
<a name="com.google.javascript-closure-compiler-externs"></a>closure-compiler-externs by com.google.javascript</h3>
<h4>
<a name="com.google.javascript-closure-compiler-externs-v20240317"></a>
           Revision: v20240317<span style="padding-left:15px;"><img src="http://ant.apache.org/ivy/images/searched.gif" alt="searched" title="required a search in repository"><img src="http://ant.apache.org/ivy/images/downloaded.gif" alt="downloaded" title="downloaded from repository"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20240319054300</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">257 kB
            (0 kB downloaded,
            257 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>v20240317</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>closure-compiler-externs</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">257 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="com.google.javascript-closure-compiler"></a>closure-compiler by com.google.javascript</h3>
<h4>
<a name="com.google.javascript-closure-compiler-v20240317"></a>
           Revision: v20240317<span style="padding-left:15px;"><img src="http://ant.apache.org/ivy/images/searched.gif" alt="searched" title="required a search in repository"><img src="http://ant.apache.org/ivy/images/downloaded.gif" alt="downloaded" title="downloaded from repository"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://developers.google.com/closure/compiler/">https://developers.google.com/closure/compiler/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20240319054448</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">13657 kB
            (0 kB downloaded,
            13657 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>v20240317</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>closure-compiler</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">13657 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.fusesource.jansi-jansi"></a>jansi by org.fusesource.jansi</h3>
<h4>
<a name="org.fusesource.jansi-jansi-1.9"></a>
           Revision: 1.9<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20120604195744</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">111 kB
            (0 kB downloaded,
            111 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>1.9</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>jansi</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">111 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="commons-io-commons-io"></a>commons-io by commons-io</h3>
<h4>
<a name="commons-io-commons-io-2.18.0"></a>
           Revision: 2.18.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://commons.apache.org/proper/commons-io/">https://commons.apache.org/proper/commons-io/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241116205703</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">526 kB
            (0 kB downloaded,
            526 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>2.18.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>commons-io</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">526 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="ant-contrib-ant-contrib"></a>ant-contrib by ant-contrib</h3>
<h4>
<a name="ant-contrib-ant-contrib-1.0b3"></a>
           Revision: 1.0b3<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://ant-contrib.sourceforge.net">http://ant-contrib.sourceforge.net</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20101020131705</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">219 kB
            (0 kB downloaded,
            219 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://ant-contrib.sourceforge.net/tasks/LICENSE.txt">Unknown License</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>1.0b3</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#ant-ant"> ant
         by
         ant</a></td><td><a href="#ant-ant-1.5">1.5</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>ant-contrib</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">219 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="ant-ant"></a>ant by ant</h3>
<h4>
<a name="ant-ant-1.5"></a>
           Revision: 1.5<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20051109035401</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">0 kB
            (0 kB downloaded,
            0 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>ant-contrib</td><td><a href="#ant-contrib-ant-contrib">ant-contrib</a></td><td>1.0b3</td><td>default, compile, runtime, master</td><td>1.5</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<tr>
<td>
        No artifact
        </td>
</tr>
</table>
<h3>
<a name="com.sencha.tools.external-yuicompressor"></a>yuicompressor by com.sencha.tools.external</h3>
<h4>
<a name="com.sencha.tools.external-yuicompressor-2.4.7"></a>
           Revision: 2.4.7<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20120703123523</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">sencha-legacy</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">870 kB
            (0 kB downloaded,
            870 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>2.4.7</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>yuicompressor</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">870 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.apache.velocity-velocity-engine-core"></a>velocity-engine-core by org.apache.velocity</h3>
<h4>
<a name="org.apache.velocity-velocity-engine-core-2.4.1"></a>
           Revision: 2.4.1<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20241014105508</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">504 kB
            (0 kB downloaded,
            504 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>2.4.1</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.7.36">1.7.36</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></td>
</tr>
<tr>
<td><a href="#org.apache.commons-commons-lang3"> commons-lang3
         by
         org.apache.commons</a></td><td><a href="#org.apache.commons-commons-lang3-3.17.0">3.17.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">658 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>velocity-engine-core</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">504 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.apache.commons-commons-lang3"></a>commons-lang3 by org.apache.commons</h3>
<h4>
<a name="org.apache.commons-commons-lang3-3.17.0"></a>
           Revision: 3.17.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://commons.apache.org/proper/commons-lang/">https://commons.apache.org/proper/commons-lang/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20240825003627</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">658 kB
            (0 kB downloaded,
            658 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.apache.velocity</td><td><a href="#org.apache.velocity-velocity-engine-core">velocity-engine-core</a></td><td>2.4.1</td><td>default, compile, runtime, master</td><td>3.17.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>commons-lang3</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">658 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="com.google.code.gson-gson"></a>gson by com.google.code.gson</h3>
<h4>
<a name="com.google.code.gson-gson-2.11.0"></a>
           Revision: 2.11.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20240520002442</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">291 kB
            (0 kB downloaded,
            291 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="https://www.apache.org/licenses/LICENSE-2.0.txt">Apache-2.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>2.11.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#com.google.errorprone-error_prone_annotations"> error_prone_annotations
         by
         com.google.errorprone</a></td><td><a href="#com.google.errorprone-error_prone_annotations-2.27.0">2.27.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0.txt">Apache 2.0</a></span></td><td align="center">19 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>gson</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">291 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="com.google.errorprone-error_prone_annotations"></a>error_prone_annotations by com.google.errorprone</h3>
<h4>
<a name="com.google.errorprone-error_prone_annotations-2.27.0"></a>
           Revision: 2.27.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20240426212609</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">19 kB
            (0 kB downloaded,
            19 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0.txt">Apache 2.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.google.code.gson</td><td><a href="#com.google.code.gson-gson">gson</a></td><td>2.11.0</td><td>default, compile, runtime, master</td><td>2.27.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>error_prone_annotations</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">19 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.slf4j-slf4j-jdk14"></a>slf4j-jdk14 by org.slf4j</h3>
<h4>
<a name="org.slf4j-slf4j-jdk14-1.6.6"></a>
           Revision: 1.6.6<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.slf4j.org">http://www.slf4j.org</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20120611201217</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">9 kB
            (0 kB downloaded,
            9 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>1.6.6</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-2.0.16">2.0.16</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">68 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.7.36">1.7.36</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 2.0.16"></td>
</tr>
<tr>
<td><a href="#org.slf4j-slf4j-api"> slf4j-api
         by
         org.slf4j</a></td><td><a href="#org.slf4j-slf4j-api-1.6.6">1.6.6</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">0 kB
    </td><td align="center"><img src="http://ant.apache.org/ivy/images/evicted.gif" alt="evicted" title="evicted by 1.7.36"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>slf4j-jdk14</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">9 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.apache.ant-ant"></a>ant by org.apache.ant</h3>
<h4>
<a name="org.apache.ant-ant-1.8.4"></a>
           Revision: 1.8.4<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20120522113129</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">1896 kB
            (0 kB downloaded,
            1896 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>1.8.4</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.apache.ant-ant-launcher"> ant-launcher
         by
         org.apache.ant</a></td><td><a href="#org.apache.ant-ant-launcher-1.8.4">1.8.4</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">18 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>ant</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">1896 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.apache.ant-ant-launcher"></a>ant-launcher by org.apache.ant</h3>
<h4>
<a name="org.apache.ant-ant-launcher-1.8.4"></a>
           Revision: 1.8.4<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20120522113350</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">18 kB
            (0 kB downloaded,
            18 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.apache.ant</td><td><a href="#org.apache.ant-ant">ant</a></td><td>1.8.4</td><td>default, compile, runtime, master</td><td>1.8.4</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>ant-launcher</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">18 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="xerces-xercesImpl"></a>xercesImpl by xerces</h3>
<h4>
<a name="xerces-xercesImpl-2.12.2"></a>
           Revision: 2.12.2<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://xerces.apache.org/xerces2-j/">https://xerces.apache.org/xerces2-j/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20220127154824</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">1412 kB
            (0 kB downloaded,
            1412 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0.txt">The Apache Software License, Version 2.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>2.12.2</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#xml-apis-xml-apis"> xml-apis
         by
         xml-apis</a></td><td><a href="#xml-apis-xml-apis-1.4.01">1.4.01</a></td><td align="center">release</td><td align="center">local-chain</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0.txt">The Apache Software License, Version 2.0</a></span><span style="padding-right:3px;"><a href="http://www.saxproject.org/copying.html">The SAX License</a></span><span style="padding-right:3px;"><a href="http://www.w3.org/TR/2004/REC-DOM-Level-3-Core-20040407/java-binding.zip">The W3C License</a></span></td><td align="center">215 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>xercesImpl</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">1412 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="xml-apis-xml-apis"></a>xml-apis by xml-apis</h3>
<h4>
<a name="xml-apis-xml-apis-1.4.01"></a>
           Revision: 1.4.01<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://xml.apache.org/commons/components/external/">http://xml.apache.org/commons/components/external/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20110820054111</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">local-chain</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">215 kB
            (0 kB downloaded,
            215 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://www.apache.org/licenses/LICENSE-2.0.txt">The Apache Software License, Version 2.0</a></span><span style="padding-right:3px;"><a href="http://www.saxproject.org/copying.html">The SAX License</a></span><span style="padding-right:3px;"><a href="http://www.w3.org/TR/2004/REC-DOM-Level-3-Core-20040407/java-binding.zip">The W3C License</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>xerces</td><td><a href="#xerces-xercesImpl">xercesImpl</a></td><td>2.12.2</td><td>default, compile, runtime, master</td><td>1.4.01</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>xml-apis</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">215 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.mozilla-rhino"></a>rhino by org.mozilla</h3>
<h4>
<a name="org.mozilla-rhino-1.7R4"></a>
           Revision: 1.7R4<span style="padding-left:15px;"><img src="http://ant.apache.org/ivy/images/searched.gif" alt="searched" title="required a search in repository"><img src="http://ant.apache.org/ivy/images/downloaded.gif" alt="downloaded" title="downloaded from repository"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://developer.mozilla.org/en/Rhino">https://developer.mozilla.org/en/Rhino</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20120620153507</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">1108 kB
            (0 kB downloaded,
            1108 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://www.mozilla.org/MPL/2.0/index.txt">Mozilla Public License, Version 2.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>1.7R4</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>rhino</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">1108 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.graalvm.js-js-scriptengine"></a>js-scriptengine by org.graalvm.js</h3>
<h4>
<a name="org.graalvm.js-js-scriptengine-21.2.0"></a>
           Revision: 21.2.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.graalvm.org/">http://www.graalvm.org/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210720165548</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">javadoc, default, system, compile, sources, provided, runtime, optional, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">105 kB
            (0 kB downloaded,
            105 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>21.2.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk"> graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>js-scriptengine</td><td>source</td><td>jar</td><td align="center">no</td><td align="center">13 kB</td>
</tr>
<tr>
<td>js-scriptengine</td><td>javadoc</td><td>jar</td><td align="center">no</td><td align="center">25 kB</td>
</tr>
<tr>
<td>js-scriptengine</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">67 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.graalvm.js-js"></a>js by org.graalvm.js</h3>
<h4>
<a name="org.graalvm.js-js-21.2.0"></a>
           Revision: 21.2.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.graalvm.org/">http://www.graalvm.org/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210720165611</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">javadoc, default, system, compile, sources, provided, runtime, optional, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">29846 kB
            (0 kB downloaded,
            29846 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span><span style="padding-right:3px;"><a href="http://opensource.org/licenses/MIT">MIT License</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>21.2.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#com.ibm.icu-icu4j"> icu4j
         by
         com.ibm.icu</a></td><td><a href="#com.ibm.icu-icu4j-69.1">69.1</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="https://raw.githubusercontent.com/unicode-org/icu/master/icu4c/LICENSE">Unicode/ICU License</a></span></td><td align="center">13044 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.regex-regex"> regex
         by
         org.graalvm.regex</a></td><td><a href="#org.graalvm.regex-regex-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">2758 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.truffle-truffle-api">--- truffle-api
         by
         org.graalvm.truffle</a></td><td><a href="#org.graalvm.truffle-truffle-api-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">8730 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">------ graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.truffle-truffle-api"> truffle-api
         by
         org.graalvm.truffle</a></td><td><a href="#org.graalvm.truffle-truffle-api-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">8730 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">--- graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk"> graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>js</td><td>javadoc</td><td>jar</td><td align="center">no</td><td align="center">8023 kB</td>
</tr>
<tr>
<td>js</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">18332 kB</td>
</tr>
<tr>
<td>js</td><td>source</td><td>jar</td><td align="center">no</td><td align="center">3491 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="com.ibm.icu-icu4j"></a>icu4j by com.ibm.icu</h3>
<h4>
<a name="com.ibm.icu-icu4j-69.1"></a>
           Revision: 69.1<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://icu-project.org/">http://icu-project.org/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210408042118</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">13044 kB
            (0 kB downloaded,
            13044 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="https://raw.githubusercontent.com/unicode-org/icu/master/icu4c/LICENSE">Unicode/ICU License</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.graalvm.js</td><td><a href="#org.graalvm.js-js">js</a></td><td>21.2.0</td><td>default, compile, runtime, master</td><td>69.1</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>icu4j</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">13044 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.graalvm.regex-regex"></a>regex by org.graalvm.regex</h3>
<h4>
<a name="org.graalvm.regex-regex-21.2.0"></a>
           Revision: 21.2.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://www.graalvm.org/">http://www.graalvm.org/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210720165902</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">2758 kB
            (0 kB downloaded,
            2758 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.graalvm.js</td><td><a href="#org.graalvm.js-js">js</a></td><td>21.2.0</td><td>default, compile, runtime, master</td><td>21.2.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.graalvm.truffle-truffle-api"> truffle-api
         by
         org.graalvm.truffle</a></td><td><a href="#org.graalvm.truffle-truffle-api-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">8730 kB
    </td><td align="center"></td>
</tr>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk">--- graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>regex</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">2758 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.graalvm.truffle-truffle-api"></a>truffle-api by org.graalvm.truffle</h3>
<h4>
<a name="org.graalvm.truffle-truffle-api-21.2.0"></a>
           Revision: 21.2.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="http://openjdk.java.net/projects/graal">http://openjdk.java.net/projects/graal</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210720170148</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">8730 kB
            (0 kB downloaded,
            8730 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.graalvm.js</td><td><a href="#org.graalvm.js-js">js</a></td><td>21.2.0</td><td>default, compile, runtime, master</td><td>21.2.0</td>
</tr>
<tr>
<td>org.graalvm.regex</td><td><a href="#org.graalvm.regex-regex">regex</a></td><td>21.2.0</td><td>compile, runtime</td><td>21.2.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#org.graalvm.sdk-graal-sdk"> graal-sdk
         by
         org.graalvm.sdk</a></td><td><a href="#org.graalvm.sdk-graal-sdk-21.2.0">21.2.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td><td align="center">590 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>truffle-api</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">8730 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.graalvm.sdk-graal-sdk"></a>graal-sdk by org.graalvm.sdk</h3>
<h4>
<a name="org.graalvm.sdk-graal-sdk-21.2.0"></a>
           Revision: 21.2.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://github.com/oracle/graal">https://github.com/oracle/graal</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20210720165914</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, runtime(*), runtime, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">590 kB
            (0 kB downloaded,
            590 kB in cache)</td>
</tr>
<tr>
<td class="title">Licenses</td><td class="value"><span style="padding-right:3px;"><a href="http://opensource.org/licenses/UPL">Universal Permissive License, Version 1.0</a></span></td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.graalvm.js</td><td><a href="#org.graalvm.js-js">js</a></td><td>21.2.0</td><td>default, compile, runtime, master</td><td>21.2.0</td>
</tr>
<tr>
<td>org.graalvm.js</td><td><a href="#org.graalvm.js-js-scriptengine">js-scriptengine</a></td><td>21.2.0</td><td>default, compile, runtime, master</td><td>21.2.0</td>
</tr>
<tr>
<td>org.graalvm.truffle</td><td><a href="#org.graalvm.truffle-truffle-api">truffle-api</a></td><td>21.2.0</td><td>compile, runtime</td><td>21.2.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>graal-sdk</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">590 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="org.apache.commons-commons-collections4"></a>commons-collections4 by org.apache.commons</h3>
<h4>
<a name="org.apache.commons-commons-collections4-4.5.0-M2"></a>
           Revision: 4.5.0-M2<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://commons.apache.org/proper/commons-collections/">https://commons.apache.org/proper/commons-collections/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20240615075904</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">javadoc, default, system, compile, sources, provided, runtime, optional, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">6580 kB
            (0 kB downloaded,
            6580 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>4.5.0-M2</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table class="deps">
<thead>
<tr>
<th>Module</th><th>Revision</th><th>Status</th><th>Resolver</th><th>Default</th><th>Licenses</th><th>Size</th><th></th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="#commons-codec-commons-codec"> commons-codec
         by
         commons-codec</a></td><td><a href="#commons-codec-commons-codec-1.17.0">1.17.0</a></td><td align="center">release</td><td align="center">maven-central</td><td align="center">false</td><td align="center"></td><td align="center">364 kB
    </td><td align="center"></td>
</tr>
</tbody>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>commons-collections4</td><td>javadoc</td><td>jar</td><td align="center">no</td><td align="center">4995 kB</td>
</tr>
<tr>
<td>commons-collections4</td><td>source</td><td>jar</td><td align="center">no</td><td align="center">754 kB</td>
</tr>
<tr>
<td>commons-collections4</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">831 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="commons-codec-commons-codec"></a>commons-codec by commons-codec</h3>
<h4>
<a name="commons-codec-commons-codec-1.17.0"></a>
           Revision: 1.17.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href="https://commons.apache.org/proper/commons-codec/">https://commons.apache.org/proper/commons-codec/</a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20240420234153</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">maven-central</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">master(*), compile, compile(*), master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">364 kB
            (0 kB downloaded,
            364 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>org.apache.commons</td><td><a href="#org.apache.commons-commons-collections4">commons-collections4</a></td><td>4.5.0-M2</td><td>optional</td><td>1.17.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>commons-codec</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">364 kB</td>
</tr>
</tbody>
</table>
<h3>
<a name="com.sencha.licenses-update"></a>update by com.sencha.licenses</h3>
<h4>
<a name="com.sencha.licenses-update-1.1.0"></a>
           Revision: 1.1.0<span style="padding-left:15px;"></span>
</h4>
<table class="header">
<tr>
<td class="title">Home Page</td><td class="value"><a href=""></a></td>
</tr>
<tr>
<td class="title">Status</td><td class="value">release</td>
</tr>
<tr>
<td class="title">Publication</td><td class="value">20250527142254</td>
</tr>
<tr>
<td class="title">Resolver</td><td class="value">artifactory</td>
</tr>
<tr>
<td class="title">Configurations</td><td class="value">default, compile, runtime, master</td>
</tr>
<tr>
<td class="title">Artifacts size</td><td class="value">1717 kB
            (0 kB downloaded,
            1717 kB in cache)</td>
</tr>
</table>
<h5>Required by</h5>
<table>
<thead>
<tr>
<th>Organisation</th><th>Name</th><th>Revision</th><th>In Configurations</th><th>Asked Revision</th>
</tr>
</thead>
<tbody>
<tr>
<td>com.sencha</td><td><a href="#com.sencha-sencha-command">sencha-command</a></td><td><EMAIL></td><td>compile, runtime</td><td>1.1.0</td>
</tr>
</tbody>
</table>
<h5>Dependencies</h5>
<table>
<tr>
<td>
    No dependency
    </td>
</tr>
</table>
<h5>Artifacts</h5>
<table>
<thead>
<tr>
<th>Name</th><th>Type</th><th>Ext</th><th>Download</th><th>Size</th>
</tr>
</thead>
<tbody>
<tr>
<td>update</td><td>jar</td><td>jar</td><td align="center">no</td><td align="center">1717 kB</td>
</tr>
</tbody>
</table>
</div>
</body>
</html>
