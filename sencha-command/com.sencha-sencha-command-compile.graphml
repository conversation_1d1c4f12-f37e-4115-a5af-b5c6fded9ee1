<?xml version="1.0" encoding="UTF-8"?><graphml xsi:schemaLocation="http://graphml.graphdrawing.org/xmlns/graphml http://www.yworks.com/xml/schema/graphml/1.0/ygraphml.xsd" xmlns="http://graphml.graphdrawing.org/xmlns/graphml" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:y="http://www.yworks.com/xml/graphml"><key id="d0" for="node" yfiles.type="nodegraphics"/><key id="d1" for="edge" yfiles.type="edgegraphics"/><graph id="G" edgedefault="directed"><node id="com.sencha-sencha-command"><data key="d0"><y:ShapeNode><y:Fill color="#CCCCFF" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">sencha-command</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.graalvm.js-js-scriptengine"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">js-scriptengine
21.2.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.graalvm.js-js"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">js
21.2.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.ibm.icu-icu4j"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">icu4j
69.1</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.graalvm.regex-regex"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">regex
21.2.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.graalvm.truffle-truffle-api"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">truffle-api
21.2.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.graalvm.sdk-graal-sdk"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">graal-sdk
21.2.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.apache.commons-commons-collections4"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">commons-collections4
4.5.0-M2</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="commons-codec-commons-codec"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">commons-codec
1.17.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="com.sencha.licenses-update"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">update
1.1.0</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><node id="org.slf4j-slf4j-api"><data key="d0"><y:ShapeNode><y:Fill color="#FFFFCC" transparent="false"/><y:BorderStyle type="line" width="1.0" color="#000000"/><y:NodeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="internal" modelPosition="c" autoSizePolicy="center">slf4j-api
1.6.6</y:NodeLabel><y:Shape type="roundrectangle"/></y:ShapeNode></data></node><edge id="com.sencha-sencha-command-org.graalvm.js-js-scriptengine" source="com.sencha-sencha-command" target="org.graalvm.js-js-scriptengine"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.graalvm.js-js" source="com.sencha-sencha-command" target="org.graalvm.js-js"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.js-js-com.ibm.icu-icu4j" source="org.graalvm.js-js" target="com.ibm.icu-icu4j"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">69.1</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.js-js-org.graalvm.regex-regex" source="org.graalvm.js-js" target="org.graalvm.regex-regex"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.js-js-org.graalvm.truffle-truffle-api" source="org.graalvm.js-js" target="org.graalvm.truffle-truffle-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.regex-regex-org.graalvm.truffle-truffle-api" source="org.graalvm.regex-regex" target="org.graalvm.truffle-truffle-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.js-js-org.graalvm.sdk-graal-sdk" source="org.graalvm.js-js" target="org.graalvm.sdk-graal-sdk"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.js-js-scriptengine-org.graalvm.sdk-graal-sdk" source="org.graalvm.js-js-scriptengine" target="org.graalvm.sdk-graal-sdk"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.graalvm.truffle-truffle-api-org.graalvm.sdk-graal-sdk" source="org.graalvm.truffle-truffle-api" target="org.graalvm.sdk-graal-sdk"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">21.2.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.apache.commons-commons-collections4" source="com.sencha-sencha-command" target="org.apache.commons-commons-collections4"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">4.5.0-M2</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="org.apache.commons-commons-collections4-commons-codec-commons-codec" source="org.apache.commons-commons-collections4" target="commons-codec-commons-codec"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.17.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-com.sencha.licenses-update" source="com.sencha-sencha-command" target="com.sencha.licenses-update"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.1.0</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge><edge id="com.sencha-sencha-command-org.slf4j-slf4j-api" source="com.sencha-sencha-command" target="org.slf4j-slf4j-api"><data key="d1"><y:PolyLineEdge><y:LineStyle type="line" width="1.0" color="#000000"/><y:Arrows source="none" target="standard"/><y:EdgeLabel visible="true" alignment="center" fontFamily="Dialog" fontSize="12" fontStyle="plain" textColor="#000000" modelName="free" modelPosition="anywhere" preferredPlacement="target" distance="2.0" ratio="0.5">1.6.6</y:EdgeLabel><y:BendStyle smoothed="false"/></y:PolyLineEdge></data></edge></graph></graphml>