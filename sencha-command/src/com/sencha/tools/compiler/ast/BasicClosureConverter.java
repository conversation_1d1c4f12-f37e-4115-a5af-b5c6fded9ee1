/*
 * Copyright (c) 2012-2016. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.google.javascript.jscomp.jarjar.com.google.common.collect.ImmutableList;
import com.google.javascript.jscomp.parsing.parser.TokenType;
import com.google.javascript.jscomp.parsing.parser.trees.*;
import com.google.javascript.jscomp.parsing.parser.trees.Comment;
import com.sencha.exceptions.ExParse;
import com.sencha.tools.compiler.ast.js.*;
import com.sencha.util.ObjectUtil;
import org.mozilla.javascript.Token;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class BasicClosureConverter extends ClosureConverter implements ClosureASTConverter {

    private String _originalSource;

    private String getOriginalSource(ParseTree tree) {
        if (tree == null || tree.location == null || _originalSource == null) {
            return "";
        }
        
        int start = tree.location.start.offset;
        int end = tree.location.end.offset;
        
        if (start >= 0 && end > start && end <= _originalSource.length()) {
            return _originalSource.substring(start, end);
        }
        
        return "";
    }

    public void setOriginalSource(String originalSource) {
        this._originalSource = originalSource;
    }
    
    // <editor-fold IRFactory utils>
    
    /**
     * Utility code from IRFactory.java in the closure jar file 
    */
    
    private static boolean isOctalDigit(char c) {
        return c >= '0' && c <= '7';
    }

    private static int octaldigit(char c) {
        if (isOctalDigit(c)) {
            return (c - '0');
        }
        throw new IllegalStateException("unexpected: " + c);
    }

    private static int hexdigit(char c) {
        switch (c) {
            case '0': return 0;
            case '1': return 1;
            case '2': return 2;
            case '3': return 3;
            case '4': return 4;
            case '5': return 5;
            case '6': return 6;
            case '7': return 7;
            case '8': return 8;
            case '9': return 9;
            case 'a': case 'A': return 10;
            case 'b': case 'B': return 11;
            case 'c': case 'C': return 12;
            case 'd': case 'D': return 13;
            case 'e': case 'E': return 14;
            case 'f': case 'F': return 15;
        }
        throw new IllegalStateException("unexpected: " + c);
    }

    private String normalizeString(String value, boolean templateLiteral) {
        if (templateLiteral) {
            // <CR><LF> and <CR> are normalized as <LF> for raw string value
            value = value.replaceAll("\r\n?", "\n");
        }
        int start = templateLiteral ? 0 : 1; // skip the leading quote
        int cur = value.indexOf('\\');
        if (cur == -1) {
            // short circuit no escapes.
            return templateLiteral ? value : value.substring(1, value.length() - 1);
        }
        StringBuilder result = new StringBuilder();
        while (cur != -1) {
            if (cur - start > 0) {
                result.append(value, start, cur);
            }
            cur += 1; // skip the escape char.
            char c = value.charAt(cur);
            switch (c) {
                case '\'':
                case '"':
                case '\\':
                    result.append(c);
                    break;
                case 'b':
                    result.append('\b');
                    break;
                case 'f':
                    result.append('\f');
                    break;
                case 'n':
                    result.append('\n');
                    break;
                case 'r':
                    result.append('\r');
                    break;
                case 't':
                    result.append('\t');
                    break;
                case 'v':
                    result.append('\u000B');
                    break;
                case '\n':
/*
                    features = features.require(FeatureSet.Feature.STRING_CONTINUATION);
                    if (isEs5OrBetterMode()) {
                        errorReporter.warning(STRING_CONTINUATION_WARNING,
                            sourceName,
                            lineno(token.location.start), charno(token.location.start));
                    } else {
                        errorReporter.error(STRING_CONTINUATION_ERROR,
                            sourceName,
                            lineno(token.location.start), charno(token.location.start));
                    }
                    // line continuation, skip the line break
*/
                    break;
                case '0': case '1': case '2': case '3': case '4': case '5': case '6': case '7':
                    char next1 = value.charAt(cur + 1);

//                    if (inStrictContext()) {
//                        if (c == '0' && !isOctalDigit(next1)) {
//                            // No warning: "\0" followed by a character which is not an octal digit
//                            // is allowed in strict mode.
//                        } else {
//                            errorReporter.warning(OCTAL_STRING_LITERAL_WARNING,
//                                sourceName,
//                                lineno(token.location.start), charno(token.location.start));
//                        }
//                    }

                    if (!isOctalDigit(next1)) {
                        result.append((char) octaldigit(c));
                    } else {
                        char next2 = value.charAt(cur + 2);
                        if (!isOctalDigit(next2)) {
                            result.append((char) (8 * octaldigit(c) + octaldigit(next1)));
                            cur += 1;
                        } else {
                            result.append((char)
                                (8 * 8 * octaldigit(c) + 8 * octaldigit(next1) + octaldigit(next2)));
                            cur += 2;
                        }
                    }

                    break;
                case 'x':
                    result.append((char) (
                        hexdigit(value.charAt(cur + 1)) * 0x10
                            + hexdigit(value.charAt(cur + 2))));
                    cur += 2;
                    break;
                case 'u':
                    int escapeEnd;
                    String hexDigits;
                    if (value.charAt(cur + 1) != '{') {
                        // Simple escape with exactly four hex digits: \\uXXXX
                        escapeEnd = cur + 5;
                        hexDigits = value.substring(cur + 1, escapeEnd);
                    } else {
                        // Escape with braces can have any number of hex digits: \\u{XXXXXXX}
                        escapeEnd = cur + 2;
                        while (Character.digit(value.charAt(escapeEnd), 0x10) >= 0) {
                            escapeEnd++;
                        }
                        hexDigits = value.substring(cur + 2, escapeEnd);
                        escapeEnd++;
                    }
                    result.append(Character.toChars(Integer.parseInt(hexDigits, 0x10)));
                    cur = escapeEnd - 1;
                    break;
                default:
                    // TODO(tbreisacher): Add a warning because the user probably
                    // intended to type an escape sequence.
                    result.append(c);
                    break;
            }
            start = cur + 1;
            cur = value.indexOf('\\', start);
        }
        // skip the trailing quote.
        result.append(value, start, templateLiteral ? value.length() : value.length() - 1);

        return result.toString();
    }
    
    //</editor-fold>

    /**
     * Creates a passthrough node that preserves the original modern syntax
     * for Closure Compiler to handle natively.
     */
    private BaseNode createPassthroughNode(ParseTree tree) {
        // Create a special AST node that preserves the original source syntax
        PassthroughNode node = new PassthroughNode();
        node.setOriginalSyntax(getOriginalSource(tree));
        node.setTreeType(tree.getClass().getSimpleName());
        return node;
    }

    private BaseNode createNodeFromToken(com.google.javascript.jscomp.parsing.parser.Token token) {
        if (token == null) {
            return null;
        }
        String value;
        KeywordLiteral literal;
        Name name;
        StringLiteral string;
        switch (token.type) {
            case IDENTIFIER:
                name = new Name();
                name.setIdentifier(token.asIdentifier().value);
                return name;
            case TEMPLATE_HEAD:
            case TEMPLATE_MIDDLE:
            case TEMPLATE_TAIL:
                name = new Name();
                value = token.asLiteral().value;
                name.setValue(value);
                return name;
            case NO_SUBSTITUTION_TEMPLATE:
                name = new Name();
                value = token.asLiteral().value;
                name.setValue(normalizeString(value, true));
                return name;
            case STRING:
                string = new StringLiteral();
                value = token.asLiteral().value;
                string.setValue(normalizeString(value, false));
                string.setQuoteCharacter(value.charAt(0));
                return string;
            case NUMBER:
                value = token.asLiteral().value;
                try {
                    NumberLiteral num = new NumberLiteral();
                    if (value.startsWith("0x")) {
                        num.setValue(Long.parseLong(value.substring(2), 16));
                    }
                    else if (value.startsWith("0c")) {
                        num.setValue(Long.parseLong(value.substring(2), 8));
                    }
                    else if (value.startsWith("0o")) {
                        num.setValue(Long.parseLong(value.substring(2), 8));
                    }
                    else if (value.startsWith("0b")) {
                        num.setValue(Long.parseLong(value.substring(2), 2));
                    }
                    else {
                        num.setValue(Double.parseDouble(value));
                    }
                    return num;
                } catch (NumberFormatException ex) {
                    name = new Name();
                    name.setValue(value);
                    return name;
                }
            case REGULAR_EXPRESSION:
                RegExpLiteral regex = new RegExpLiteral();
                value = token.asLiteral().toString();
                int lastIndex = value.lastIndexOf('/');
                regex.setValue(value.substring(1, lastIndex));
                if (lastIndex > -1) {
                    regex.setFlags(value.substring(lastIndex+1));
                }
                return regex;
            case TRUE:
                literal = new KeywordLiteral();
                literal.setCode(Token.TRUE);
                return literal;
            case FALSE:
                literal = new KeywordLiteral();
                literal.setCode(Token.FALSE);
                return literal;
            case NULL:
                literal = new KeywordLiteral();
                literal.setCode(Token.NULL);
                return literal;
            default:
                break;
        }
        return null;
    }

    private BaseNode getNodeFromToken(com.google.javascript.jscomp.parsing.parser.Token token) {
        BaseNode node = createNodeFromToken(token);
        if (node != null) {
            node.setFileName(_sourceName);
            node.setLine(token.location.start.line);
            node.setOffset(token.location.start.offset);
            node.setPosition(token.location.start.offset);
        }
        return node;
    }

    private BaseNode convertParseTreeCollection(ImmutableList<ParseTree> collection) {
        if (collection == null) {
            return null;
        }

        BaseNode node = null;
        for (ParseTree t: collection) {
            BaseNode element = doConvert(t, null);
            if (node == null) {
                node = element;
            }
            else {
                Infix infix = new Infix();
                infix.setOperator(Operators.getByValue(","));
                infix.setLeft(node);
                infix.setRight(element);
                node = infix;
            }
        }
        return node;
    }

//    @Override
//    public BaseNode convert(AmbientDeclarationTree tree) {
//        return doConvert(tree.declaration, null);
//    }

    @Override
    public BaseNode convert(ArgumentListTree tree) {
        return convertParseTreeCollection(tree.arguments);
    }

    @Override
    public BaseNode convert(ArrayLiteralExpressionTree tree) {
        ArrayLiteral node = new ArrayLiteral();
        for (ParseTree element: tree.elements) {
            node.addElement(doConvert(element, node));
        }
        return node;
    }

    @Override
    public BaseNode convert(ArrayPatternTree tree) {
        ArrayPattern node = new ArrayPattern();
        if (tree.elements != null) {
            for (ParseTree t: tree.elements) {
                node.addElement(doConvert(t, node));
            }
        }
        return node;
    }

//    @Override
//    public BaseNode convert(ArrayTypeTree tree) {
//        return null;
//    }

//    @Override
//    public BaseNode convert(AssignmentRestElementTree tree) {
//        RestParameter node = new RestParameter();
//        node.setOperand(doConvert(tree.assignmentTarget, node));
//        return node;
//    }

    @Override
    public BaseNode convert(AwaitExpressionTree tree) {
        AwaitExpression node = new AwaitExpression();
        node.setExpr(doConvert(tree.expression, node));
        return node;
    }

    @Override
    public BaseNode convert(BinaryOperatorTree tree) {
        Infix node;
        String operator = tree.operator.type.toString();

        if ("=".equals(operator)) {
            node = new Assignment();
        }
        else {
            node = new Infix();
        }
        node.setLeft(doConvert(tree.left, node));
        node.setRight(doConvert(tree.right, node));
        node.setOperator(Operators.getByValue(operator));
        return node;
    }

    @Override
    public BaseNode convert(BlockTree tree) {
        Block node = new Block();
        for (ParseTree element: tree.statements) {
            node.addElement(doConvert(element, node));
        }
        return node;
    }

    @Override
    public BaseNode convert(BreakStatementTree tree) {
        BreakStatement node = new BreakStatement();
        if (tree.name != null) {
            node.setLabel((Name) getNodeFromToken(tree.name));
        }
        return node;
    }

    @Override
    public BaseNode convert(CallExpressionTree tree) {
        FunctionCall node = new FunctionCall();
        node.setTarget(doConvert(tree.operand, node));
        for(ParseTree arg: tree.arguments.arguments) {
            node.addArgument(doConvert(arg, node));
        }
        return node;
    }

//    @Override
//    public BaseNode convert(CallSignatureTree tree) {
//        return null;
//    }

    @Override
    public BaseNode convert(CaseClauseTree tree) {
        SwitchCase node = new SwitchCase();
        node.setExpression(doConvert(tree.expression, node));
        for (ParseTree t: tree.statements) {
            node.addElement(doConvert(t, node));
        }
        return node;
    }

    @Override
    public BaseNode convert(CatchTree tree) {
        CatchClause node = new CatchClause();
        node.setName((Name) doConvert(tree.exception, node));
        node.setBody((Block) doConvert(tree.catchBody, node));
        return node;
    }

    @Override
    public BaseNode convert(ClassDeclarationTree tree) {
        ClassDeclaration node = new ClassDeclaration();
        node.setName(getNodeFromToken(tree.name));
        node.setSuperClass(doConvert(tree.superClass, node));
        if (tree.elements != null) {
            for (ParseTree t: tree.elements) {
                node.addInterface(doConvert(t, node));
            }
        }
        if (tree.elements != null) {
            for (ParseTree t: tree.elements) {
                node.addElement(doConvert(t, node));
            }
        }
        return node;
    }

    @Override
    public BaseNode convert(CommaExpressionTree tree) {
        return convertParseTreeCollection(tree.expressions);
    }

    @Override
    public BaseNode convert(Comment tree) {
        if (tree.type == Comment.Type.BLOCK || 
            tree.type == Comment.Type.JSDOC) {
            BlockComment block = new BlockComment();
            block.setValue(tree.value);
            block.setFileName(_sourceName);
            block.setLine(tree.location.start.line);
            block.setOffset(tree.location.start.offset);
            block.setPosition(tree.location.start.offset);
            return block;
        }
        else {
            LineComment line = new LineComment();
            line.setValue(tree.value);
            line.setFileName(_sourceName);
            line.setLine(tree.location.start.line);
            line.setOffset(tree.location.start.offset);
            line.setPosition(tree.location.start.offset);
            return line;
        }
    }

    @Override
    public BaseNode convert(ComprehensionForTree tree) {
        return null;
    }

    @Override
    public BaseNode convert(ComprehensionIfTree tree) {
        return null;
    }

    @Override
    public BaseNode convert(ComprehensionTree tree) {
        return null;
    }

    @Override
    public BaseNode convert(ComputedPropertyDefinitionTree tree) {
        ObjectProperty node = new ObjectProperty();
        ComputedName name = new ComputedName();
        name.setExpr(doConvert(tree.property, node));
        node.setName(name);
        node.setValue(doConvert(tree.value, node));
        return node;
    }

    @Override
    public BaseNode convert(ComputedPropertyGetterTree tree) {
        GetAccessor node = new GetAccessor();
        ComputedName name = new ComputedName();
        name.setExpr(doConvert(tree.property, node));
        node.setName(name);
        node.setReturnType(doConvert(getReturnType(tree.body.statements), node));
        node.setBody(doConvert(tree.body, node));
        node.setStatic(tree.isStatic);
        return node;
    }

//    @Override
//    public BaseNode convert(ComputedPropertyMemberVariableTree tree) {
//        return null;
//    }

    @Override
    public BaseNode convert(ComputedPropertyMethodTree tree) {
        FunctionNode node = (FunctionNode) convert((FunctionDeclarationTree)tree.method);
        ComputedName name = new ComputedName();
        name.setExpr(doConvert(tree.property, node));
        node.setName(name);
        node.setFunctionType(FunctionNode.FunctionType.Member);
        return node;
    }

    @Override
    public BaseNode convert(ComputedPropertySetterTree tree) {
        SetAccessor node = new SetAccessor();
        ComputedName name = new ComputedName();
        name.setExpr(doConvert(tree.property, node));
        node.setName(name);
        node.setParameter(doConvert(tree.parameter, node));
        // TODO node.setType(doConvert(tree.type, node));
        node.setBody(doConvert(tree.body, node));
        node.setStatic(tree.isStatic);
        return node;
    }

    @Override
    public BaseNode convert(ConditionalExpressionTree tree) {
        ConditionalExpression node = new ConditionalExpression();
        node.setTest(doConvert(tree.condition, node));
        node.setTrue(doConvert(tree.left, node));
        node.setFalse(doConvert(tree.right, node));
        return node;
    }

    @Override
    public BaseNode convert(ContinueStatementTree tree) {
        ContinueStatement node = new ContinueStatement();
        node.setLabel(getNodeFromToken(tree.name));
        return node;
    }

    @Override
    public BaseNode convert(DebuggerStatementTree tree) {
        KeywordLiteral node = new KeywordLiteral();
        node.setCode(Token.DEBUGGER);
        return node;
    }

    @Override
    public BaseNode convert(DefaultClauseTree tree) {
        SwitchCase node = new SwitchCase();
        // SwitchCase with null expression is default
        for (ParseTree t: tree.statements) {
            node.addElement(doConvert(t, node));
        }
        return node;
    }

    @Override
    public BaseNode convert(DefaultParameterTree tree) {
        DefaultParameter node = new DefaultParameter();
        node.setName(doConvert(tree.lhs, node));
        node.setDefaultValue(doConvert(tree.defaultValue, node));
        return node;
    }

    @Override
    public BaseNode convert(DoWhileStatementTree tree) {
        DoLoop node = new DoLoop();
        node.setCondition(doConvert(tree.condition, node));
        node.setBody(doConvert(tree.body, node));
        return node;
    }

    // TODO
    @Override
    public BaseNode convert(DynamicImportTree tree) {// New
       return null;
    }

    @Override
    public BaseNode convert(EmptyStatementTree tree) {
        return new EmptyExpression();
    }

//    @Override
//    public BaseNode convert(EnumDeclarationTree tree) {
//        return null;
//    }

    @Override
    public BaseNode convert(ExportDeclarationTree tree) {
        ExportDeclaration node = new ExportDeclaration();
        node.setDefault(tree.isDefault);
        node.setExportAll(tree.isExportAll);
        node.setDeclaration(doConvert(tree.declaration, node));
        node.setFrom(getNodeFromToken(tree.from));
        if (tree.exportSpecifierList != null) {
            for (ParseTree t: tree.exportSpecifierList) {
                node.addExportSpecifier(doConvert(t, node));
            }
        }
        return node;
    }

    @Override
    public BaseNode convert(ExportSpecifierTree tree) {
        ExportSpecifier node = new ExportSpecifier();
        node.setImportedName(getNodeFromToken(tree.importedName));
        node.setDestination(getNodeFromToken(tree.destinationName));
        return node;
    }

    @Override
    public BaseNode convert(ExpressionStatementTree tree) {
        ExpressionStatement node = new ExpressionStatement();
        node.setExpression(doConvert(tree.expression, node));
        return node;
    }

    // TODO
    @Override
    public BaseNode convert(FieldDeclarationTree tree) { // New
        return null;
    }

    @Override
    public BaseNode convert(FinallyTree tree) {
        return doConvert(tree.block, null);
    }

    @Override
    public BaseNode convert(ForAwaitOfStatementTree tree) {
        ForAwaitOfStatement node = new ForAwaitOfStatement();
        node.setCollection(doConvert(tree.collection,node));
        node.setInitializer(doConvert(tree.initializer, node));
        node.setBody(doConvert(tree.body, node));
        return node;
    }

    @Override
    public BaseNode convert(ForInStatementTree tree) {
        ForInLoop node = new ForInLoop();
        node.setForEach(false);
        node.setIterator(doConvert(tree.initializer, node));
        node.setIteratedObject(doConvert(tree.collection, node));
        node.setBody(doConvert(tree.body, node));
        return node;
    }

    @Override
    public BaseNode convert(ForOfStatementTree tree) {
        ForOfLoop node = new ForOfLoop();
        node.setForEach(false);
        node.setIterator(doConvert(tree.initializer, node));
        node.setIteratedObject(doConvert(tree.collection, node));
        node.setBody(doConvert(tree.body, node));
        return node;
    }

    @Override
    public BaseNode convert(ForStatementTree tree) {
        ForLoop node = new ForLoop();
        node.setInitializer(doConvert(tree.initializer, node));
        node.setCondition(doConvert(tree.condition, node));
        node.setIncrement(doConvert(tree.increment, node));
        node.setBody(doConvert(tree.body, node));
        return node;
    }

    @Override
    public BaseNode convert(FormalParameterListTree tree) {
        FormalParameterList node = new FormalParameterList();
        for (ParseTree t: tree.parameters) {
            node.addParam(doConvert(t, node));
        }
        return node;
    }

    @Override
    public BaseNode convert(FunctionDeclarationTree tree) {
        FunctionNode node = new FunctionNode();
        node.setName((Name) getNodeFromToken(tree.name));
        for (ParseTree t: tree.formalParameterList.parameters) {
            node.addParam(doConvert(t, node));
        }
        node.setStatic(tree.isStatic);
        node.setAsync(tree.isAsync);
        node.setGenerator(tree.isGenerator);
        node.setOptional(tree.isOptional);
        node.setBody(doConvert(tree.functionBody, node));
        if (tree.kind == FunctionDeclarationTree.Kind.ARROW) {
            node.setFunctionType(FunctionNode.FunctionType.Arrow);
        }
        if (tree.kind == FunctionDeclarationTree.Kind.MEMBER) {
            node.setFunctionType(FunctionNode.FunctionType.Member);
        }
        return node;
    }

//    @Override
//    public BaseNode convert(FunctionTypeTree tree) {
//        return null;
//    }

//    @Override
//    public BaseNode convert(GenericTypeListTree tree) {
//        return null;
//    }

    @Override
    public BaseNode convert(GetAccessorTree tree) {
        GetAccessor node = new GetAccessor();
        node.setName(getNodeFromToken(tree.propertyName));
        node.setReturnType(doConvert(getReturnType(tree.body.statements), node));
        node.setBody(doConvert(tree.body, node));
        node.setStatic(tree.isStatic);
        return node;
    }

    @Override
    public BaseNode convert(IdentifierExpressionTree tree) {
        return getNodeFromToken(tree.identifierToken);
    }

    @Override
    public BaseNode convert(IfStatementTree tree) {
        IfStatement node = new IfStatement();
        node.setCondition(doConvert(tree.condition, node));
        node.setThen(doConvert(tree.ifClause, node));
        node.setElse(doConvert(tree.elseClause, node));
        return node;
    }

    // TODO
    @Override
    public BaseNode convert(ImportMetaExpressionTree tree) {
        return null;
    }

    @Override
    public BaseNode convert(ImportDeclarationTree tree) {
        ImportDeclaration node = new ImportDeclaration();
        node.setNamespace(getNodeFromToken(tree.nameSpaceImportIdentifier));
        node.setModule(getNodeFromToken(tree.moduleSpecifier));
        node.setDefaultBinding(getNodeFromToken(tree.defaultBindingIdentifier));
        if (tree.importSpecifierList != null) {
            for (ParseTree t: tree.importSpecifierList) {
                node.addImport(doConvert(t, node));
            }
        }
        return node;
    }

    @Override
    public BaseNode convert(ImportSpecifierTree tree) {
        ImportSpecifier node = new ImportSpecifier();
        node.setImportName(getNodeFromToken(tree.importedName));
        node.setDestination(getNodeFromToken(tree.destinationName));
        return node;
    }


    @Override
    public BaseNode convert(IterRestTree tree) { // New
        RestParameter node = new RestParameter();
        node.setOperand(doConvert(tree.assignmentTarget, node));
        return node;
    }


    @Override
    public BaseNode convert(IterSpreadTree tree) { // New
        SpreadExpression node = new SpreadExpression();
        node.setOperand(doConvert(tree.expression, node));
        return node;
    }

//    @Override
//    public BaseNode convert(IndexSignatureTree tree) {
//        return null;
//    }

//    @Override
//    public BaseNode convert(InterfaceDeclarationTree tree) {
//        return null;
//    }
//    }

    @Override
    public BaseNode convert(LabelledStatementTree tree) {
        LabeledStatement node = new LabeledStatement();
        Label lbl = new Label();
        lbl.setName(tree.name.asIdentifier().value);
        node.addLabel(lbl);
        node.setStatement(doConvert(tree.statement, node));
        return node;
    }

    @Override
    public BaseNode convert(LiteralExpressionTree tree) {
        return getNodeFromToken(tree.literalToken);
    }

    @Override
    public BaseNode convert(MemberExpressionTree tree) {
        PropertyGet node = new PropertyGet();
        node.setLeft(doConvert(tree.operand, node));
        node.setRight(getNodeFromToken(tree.memberName));
        return node;
    }

    @Override
    public BaseNode convert(MemberLookupExpressionTree tree) {
        ElementGet node = new ElementGet();
        node.setTarget(doConvert(tree.operand, node));
        node.setElement(doConvert(tree.memberExpression, node));
        return node;
    }

//    @Override
//    public BaseNode convert(MemberVariableTree tree) {
//        return null;
//    }

    @Override
    public BaseNode convert(MissingPrimaryExpressionTree tree) {
        return null;
    }

//    @Override
//    public BaseNode convert(NamespaceDeclarationTree tree) {
//        return null;
//    }

//    @Override
//    public BaseNode convert(NamespaceNameTree tree) {
//        return null;
//    }

    @Override
    public BaseNode convert(NewExpressionTree tree) {
        NewExpression node = new NewExpression();
        node.setTarget(doConvert(tree.operand, node));
        if (tree.arguments != null) {
            for (ParseTree t: tree.arguments.arguments) {
                node.addArgument(doConvert(t, node));
            }
        }
        return node;
    }

    @Override
    public BaseNode convert(NewTargetExpressionTree tree) {
        return null;
    }

    @Override
    public BaseNode convert(NullTree tree) {
        return new EmptyExpression();
    }

    @Override
    public BaseNode convert(ObjectLiteralExpressionTree tree) {
        ObjectLiteral node = new ObjectLiteral();
        for (ParseTree t: tree.propertyNameAndValues) {
            node.addProperty(doConvert(t, node));
        }
        return node;
    }

    @Override
    public BaseNode convert(ObjectPatternTree tree) {
        ObjectPattern node = new ObjectPattern();
        if (tree.fields != null) {
            for (ParseTree t: tree.fields) {
                node.addProperty(doConvert(t, node));
            }
        }
        return node;
    }


    @Override
    public BaseNode convert(ObjectRestTree tree) { // New
        RestParameter node = new RestParameter();
        node.setOperand(doConvert(tree.assignmentTarget, node));
        return node;
    }

    // TODO
    @Override
    public BaseNode convert(ObjectSpreadTree tree) { // New
        ObjectSpread node = new ObjectSpread();
        node.setOperand(doConvert(tree.expression, node));
        return node;
    }


    @Override
    public BaseNode convert(OptChainCallExpressionTree tree) { // New
        OptionalMemberExpression node = new OptionalMemberExpression();
        node.setLeft(doConvert(tree.operand, node));
        node.setRight(doConvert(tree.arguments, node));
        node.setStartOfOptionalChain(tree.isStartOfOptionalChain);
        node.setTrailingComma(tree.hasTrailingComma);
        node.setOperator(node.getOperator());
        return node;
    }

    @Override
    public BaseNode convert(OptionalMemberExpressionTree tree) {
        OptionalMemberExpression node = new OptionalMemberExpression();
        node.setLeft(doConvert(tree.operand, node));
        node.setRight(getNodeFromToken(tree.memberName));
        node.setStartOfOptionalChain(tree.isStartOfOptionalChain);
        node.setOperator(node.getOperator());
        return node;
    }


    @Override
    public BaseNode convert(OptionalMemberLookupExpressionTree tree) { // New
        OptionalMemberLookUpExpression node = new OptionalMemberLookUpExpression();
        node.setLeft(doConvert(tree.operand, node));
        node.setRight(doConvert(tree.memberExpression,node));
        node.setStartOfOptionalChain(tree.isStartOfOptionalChain);
        node.setOperator(node.getOperator());
        return node;
    }

//    @Override
//    public BaseNode convert(OptionalParameterTree tree) {
//        return null;
//    }

//    @Override
//    public BaseNode convert(ParameterizedTypeTree tree) {
//        return null;
//    }

    @Override
    public BaseNode convert(ParenExpressionTree tree) {
        ParenthesizedExpression node = new ParenthesizedExpression();
        node.setExpr(doConvert(tree.expression, node));
        return node;
    }

    // TODO
    @Override
    public BaseNode convert(ParseTree tree) { // New
       return null;
    }

    private List<CommentWrapper> _unclaimedComments = new ArrayList<CommentWrapper>();

    @Override
    public BaseNode convert(ProgramTree tree) {
        RootNode node = new RootNode();
        for (Comment c: tree.sourceComments) {
            CommentWrapper wrapper = new CommentWrapper();
            wrapper.setComment(c);
            wrapper.setAbsPosition(c.getAbsolutePosition());
            _unclaimedComments.add(wrapper);
        }

        for(ParseTree t: tree.sourceElements) {
            node.addElement(doConvert(t, node));
        }

        for (CommentWrapper wrapper: _unclaimedComments) {
            Comment c = wrapper.getComment();
            node.addComment((com.sencha.tools.compiler.ast.js.Comment) convert(c));
        }

        return node;
    }

    @Override
    public BaseNode convert(PropertyNameAssignmentTree tree) {
        ObjectProperty node = new ObjectProperty();
        node.setName(getNodeFromToken(tree.name));
        node.setValue(doConvert(tree.value, node));
        return node;
    }

//    @Override
//    public BaseNode convert(RecordTypeTree tree) {
//        return null;
//    }

//    @Override
//    public BaseNode convert(RestParameterTree tree) {
//        RestParameter node = new RestParameter();
//        node.setOperand(doConvert(tree.assignmentTarget, node));
//        return node;
//    }

    @Override
    public BaseNode convert(ReturnStatementTree tree) {
        ReturnStatement node = new ReturnStatement();
        node.setReturnValue(doConvert(tree.expression, node));
        return node;
    }

    @Override
    public BaseNode convert(SetAccessorTree tree) {

        SetAccessor node = new SetAccessor();

        BaseNode param = doConvert(tree.parameter, node);

        node.setStatic(tree.isStatic);
        node.setName(getNodeFromToken(tree.propertyName));
        node.setParameter(param);
        //node.setType(doConvert(tree, node));
        node.setBody(doConvert(tree.body, node));
        return node;
    }

//    @Override
//    public BaseNode convert(SpreadExpressionTree tree) {
//        SpreadExpression node = new SpreadExpression();
//        node.setOperand(doConvert(tree.expression, node));
//        return node;
//    }

    @Override
    public BaseNode convert(SuperExpressionTree tree) {
        Name node = new Name();
        node.setIdentifier("super");
        return node;
    }

    @Override
    public BaseNode convert(SwitchStatementTree tree) {
        SwitchStatement node = new SwitchStatement();
        node.setExpr(doConvert(tree.expression, node));
        for (ParseTree clause: tree.caseClauses) {
            node.addElement((SwitchCase) doConvert(clause, node));
        }
        return node;
    }

    @Override
    public BaseNode convert(TemplateLiteralExpressionTree tree) {
        TemplateLiteralExpression node = new TemplateLiteralExpression();
        node.setOperand(doConvert(tree.operand, node));
        if (tree.elements != null) {
            for (ParseTree t: tree.elements) {
                node.addElement(doConvert(t, node));
            }
        }
        return node;
    }

    @Override
    public BaseNode convert(TemplateLiteralPortionTree tree) {
        TemplateLiteralPortion node = new TemplateLiteralPortion();
        node.setValue(getNodeFromToken(tree.value));
        return node;
    }

    @Override
    public BaseNode convert(TemplateSubstitutionTree tree) {
        TemplateSubstitution node = new TemplateSubstitution();
        node.setExpr(doConvert(tree.expression, node));
        return node;
    }

    @Override
    public BaseNode convert(ThisExpressionTree tree) {
        KeywordLiteral node = new KeywordLiteral();
        node.setCode(Token.THIS);
        return node;
    }

    @Override
    public BaseNode convert(ThrowStatementTree tree) {
        ThrowStatement node = new ThrowStatement();
        node.setExpr(doConvert(tree.value, node));
        return node;
    }

    @Override
    public BaseNode convert(TryStatementTree tree) {
        TryStatement node = new TryStatement();
        node.setTryBlock(doConvert(tree.body, node));
        if (tree.catchBlock != null) {
            node.addCatchClause(doConvert(tree.catchBlock, node));
        }
        node.setFinallyBlock(doConvert(tree.finallyBlock, node));
        return node;
    }

//    @Override
//    public BaseNode convert(TypeAliasTree tree) {
//        return null;
//    }

//    @Override
//    public BaseNode convert(TypeNameTree tree) {
//        return null;
//    }

//    @Override
//    public BaseNode convert(TypeQueryTree tree) {
//        return null;
//    }

//    @Override
//    public BaseNode convert(TypedParameterTree tree) {
//        return null;
//    }

    @Override
    public BaseNode convert(UnaryExpressionTree tree) {
        Unary node = new Unary();
        node.setOperand(doConvert(tree.operand, node));
        node.setOperator(
                Operators.getByValue(ObjectUtil.defaultObject(tree.operator.type.value, tree.operator.toString())));
        return node;
    }

//    @Override
//    public BaseNode convert(UnionTypeTree tree) {
//        return null;
//    }

    @Override
    public BaseNode convert(UpdateExpressionTree tree) {
        Unary node = new Unary();
        node.setOperand(doConvert(tree.operand, node));
        node.setOperator(Operators.getByValue(tree.operator.type.value));
        node.setPostfix(tree.operatorPosition == UpdateExpressionTree.OperatorPosition.POSTFIX);
        return node;
    }

    @Override
    public BaseNode convert(VariableDeclarationListTree tree) {
        VariableDeclaration node = new VariableDeclaration();
        for (ParseTree t: tree.declarations) {
            node.addVariable((VariableInitializer) doConvert(t, node));
        }
        node.setLetInitializer(tree.declarationType == TokenType.LET);
        node.setConst(tree.declarationType == TokenType.CONST);
        return node;
    }

    @Override
    public BaseNode convert(VariableDeclarationTree tree) {
        VariableInitializer node = new VariableInitializer();
        node.setTarget(doConvert(tree.lvalue, node));
        node.setInitializer(doConvert(tree.initializer, node));
        return node;
    }

    @Override
    public BaseNode convert(VariableStatementTree tree) {
        VariableDeclaration node = (VariableDeclaration) convert(tree.declarations);
        node.setStatement(true);
        return node;
    }

    @Override
    public BaseNode convert(WhileStatementTree tree) {
        WhileLoop node = new WhileLoop();
        node.setCondition(doConvert(tree.condition, node));
        node.setBody(doConvert(tree.body, node));
        return node;
    }

    @Override
    public BaseNode convert(WithStatementTree tree) {
        WithStatement node = new WithStatement();
        node.setExpr(doConvert(tree.expression, node));
        node.setStatement(doConvert(tree.body, node));
        return node;
    }

    @Override
    public BaseNode convert(YieldExpressionTree tree) {
        YieldStatement node = new YieldStatement();
        node.setValue(doConvert(tree.expression, node));
        return node;
    }

    int _absolutePosition = 0;

    @Override
    public BaseNode doConvert(ParseTree tree, BaseNode parent) {
        if (tree == null) {
            return null;
        }

        int positionWas = _absolutePosition;
        int absPosition = tree.location.start.offset;
        _absolutePosition = absPosition;

        List<CommentWrapper> comments = new LinkedList<CommentWrapper>();
        while (!_unclaimedComments.isEmpty() &&
                _absolutePosition > _unclaimedComments.get(0).getAbsPosition() &&
                !(tree instanceof ParenExpressionTree)) {
            comments.add(_unclaimedComments.remove(0));
        }

        ClosureNodeConverter converter = getConverterES5Only(tree);

        BaseNode baseNode;
        if (converter != null) {
            baseNode = converter.convert(tree, this);
        }
        else {
            // throw new ExParse("Cannot convert node type : " + tree.getClass().getCanonicalName());
            baseNode = createPassthroughNode(tree);
        }

        _absolutePosition = positionWas;

        if(baseNode != null) {
            baseNode.setParent(parent);
            baseNode.setFileName(_sourceName);
            baseNode.setLine(tree.location.start.line);
            baseNode.setOffset(tree.location.start.offset);
            baseNode.setPosition(absPosition);
            for(CommentWrapper comment : comments) {
                baseNode.addComment(
                        (com.sencha.tools.compiler.ast.js.Comment) convert(comment.getComment()));
            }
        } else {
            _unclaimedComments.addAll(0, comments);
        }
        return baseNode;

    }

    private ParseTree getReturnType(ImmutableList<ParseTree> tree) {
        if (tree == null) {
            return null;
        }
        ParseTree returnTypeTree = null;
        for (ParseTree statement : tree) {
            if (statement instanceof ReturnStatementTree) {
                returnTypeTree = statement;
                break;
            }
        }
        return returnTypeTree;
    }

    private String _sourceName;

    public String getSourceName() {
        return _sourceName;
    }

    public void setSourceName(String sourceName) {
        _sourceName = sourceName;
    }


    private class CommentWrapper {
        private Comment _comment;
        private int _absPosition;

        public Comment getComment() {
            return _comment;
        }

        public void setComment(Comment comment) {
            _comment = comment;
        }

        public int getAbsPosition() {
            return _absPosition;
        }

        public void setAbsPosition(int absPosition) {
            _absPosition = absPosition;
        }
    }
}
