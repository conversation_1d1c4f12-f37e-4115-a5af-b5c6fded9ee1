/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.sencha.tools.compiler.ast.js.*;
import com.sencha.util.CollectionUtil;
import org.mozilla.javascript.Node;
import org.mozilla.javascript.Token;
import org.mozilla.javascript.ast.*;
import org.mozilla.javascript.ast.ArrayComprehension;
import org.mozilla.javascript.ast.ArrayComprehensionLoop;
import org.mozilla.javascript.ast.ArrayLiteral;
import org.mozilla.javascript.ast.Assignment;
import org.mozilla.javascript.ast.Block;
import org.mozilla.javascript.ast.BreakStatement;
import org.mozilla.javascript.ast.CatchClause;
import org.mozilla.javascript.ast.Comment;
import org.mozilla.javascript.ast.ConditionalExpression;
import org.mozilla.javascript.ast.ContinueStatement;
import org.mozilla.javascript.ast.DoLoop;
import org.mozilla.javascript.ast.ElementGet;
import org.mozilla.javascript.ast.EmptyExpression;
import org.mozilla.javascript.ast.ErrorNode;
import org.mozilla.javascript.ast.ExpressionStatement;
import org.mozilla.javascript.ast.ForInLoop;
import org.mozilla.javascript.ast.ForLoop;
import org.mozilla.javascript.ast.FunctionCall;
import org.mozilla.javascript.ast.FunctionNode;
import org.mozilla.javascript.ast.IfStatement;
import org.mozilla.javascript.ast.KeywordLiteral;
import org.mozilla.javascript.ast.Label;
import org.mozilla.javascript.ast.LabeledStatement;
import org.mozilla.javascript.ast.LetNode;
import org.mozilla.javascript.ast.Loop;
import org.mozilla.javascript.ast.Name;
import org.mozilla.javascript.ast.NewExpression;
import org.mozilla.javascript.ast.NumberLiteral;
import org.mozilla.javascript.ast.ObjectLiteral;
import org.mozilla.javascript.ast.ObjectProperty;
import org.mozilla.javascript.ast.ParenthesizedExpression;
import org.mozilla.javascript.ast.PropertyGet;
import org.mozilla.javascript.ast.RegExpLiteral;
import org.mozilla.javascript.ast.ReturnStatement;
import org.mozilla.javascript.ast.Scope;
import org.mozilla.javascript.ast.ScriptNode;
import org.mozilla.javascript.ast.StringLiteral;
import org.mozilla.javascript.ast.SwitchCase;
import org.mozilla.javascript.ast.SwitchStatement;
import org.mozilla.javascript.ast.ThrowStatement;
import org.mozilla.javascript.ast.TryStatement;
import org.mozilla.javascript.ast.VariableDeclaration;
import org.mozilla.javascript.ast.VariableInitializer;
import org.mozilla.javascript.ast.WhileLoop;
import org.mozilla.javascript.ast.WithStatement;
import org.mozilla.javascript.ast.XmlFragment;
import org.mozilla.javascript.ast.XmlLiteral;
import org.mozilla.javascript.ast.XmlRef;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.SortedSet;

public class BasicRhinoConverter extends RhinoConverter implements RhinoAstConverter {


    private BaseNode convertInfix(Infix infix, InfixExpression node) {
        infix.setLeft(doConvert(node.getLeft(), infix));
        infix.setOperator(Operators.getByCode(node.getType()));
        infix.setRight(doConvert(node.getRight(), infix));
        return infix;
    }
    private String _sourceName;

    @Override
    public BaseNode convert (Assignment node) {
        com.sencha.tools.compiler.ast.js.Assignment assignment =
            new com.sencha.tools.compiler.ast.js.Assignment();
       return convertInfix(assignment, node);
    }

    @Override
    public BaseNode convert (PropertyGet node) {
        com.sencha.tools.compiler.ast.js.PropertyGet prop =
            new com.sencha.tools.compiler.ast.js.PropertyGet();
        return convertInfix(prop, node);
    }

    @Override
    public BaseNode convert (ArrayLiteral node) {
        com.sencha.tools.compiler.ast.js.ArrayLiteral lit =
            new com.sencha.tools.compiler.ast.js.ArrayLiteral();
        for(AstNode child : node.getElements()) {
            lit.addElement(doConvert(child, lit));
        }
        return lit;
    }

    @Override
    public BaseNode convert (Block node) {
        com.sencha.tools.compiler.ast.js.Block block =
            new com.sencha.tools.compiler.ast.js.Block();
        for(Node child : node) {
            block.addElement(doConvert((AstNode) child, block));
        }
        return block;
    }

    @Override
    public BaseNode convert (CatchClause node) {
        com.sencha.tools.compiler.ast.js.CatchClause clause =
            new com.sencha.tools.compiler.ast.js.CatchClause();
        clause.setName((com.sencha.tools.compiler.ast.js.Name) doConvert(node.getVarName(), clause));
        clause.setCondition(doConvert(node.getCatchCondition(), clause));
        clause.setBody((com.sencha.tools.compiler.ast.js.Block) doConvert(node.getBody(), clause));
        return clause;
    }

    @Override
    public BaseNode convert (Comment node) {
        com.sencha.tools.compiler.ast.js.Comment comment;
        if(node.getCommentType() == Token.CommentType.BLOCK_COMMENT) {
            comment = new BlockComment();
        } else {
            comment = new LineComment();
        }
        comment.setValue(node.getValue());
        return comment;
    }

    @Override
    public BaseNode convert (ConditionalExpression node) {
        com.sencha.tools.compiler.ast.js.ConditionalExpression cond =
            new com.sencha.tools.compiler.ast.js.ConditionalExpression();
        cond.setTest(doConvert(node.getTestExpression(), cond));
        cond.setTrue(doConvert(node.getTrueExpression(), cond));
        cond.setFalse(doConvert(node.getFalseExpression(), cond));
        return cond;
    }

    @Override
    public BaseNode convert (ElementGet node) {
        com.sencha.tools.compiler.ast.js.ElementGet get =
            new com.sencha.tools.compiler.ast.js.ElementGet();
        get.setTarget(doConvert(node.getTarget(), get));
        get.setElement(doConvert(node.getElement(), get));
        return get;
    }

    @Override
    public BaseNode convert (EmptyExpression node) {
        return new com.sencha.tools.compiler.ast.js.EmptyExpression();
    }

    @Override
    public BaseNode convert (ErrorNode node) {
        com.sencha.tools.compiler.ast.js.ErrorNode err =
            new com.sencha.tools.compiler.ast.js.ErrorNode();
        err.setMessage(node.getMessage());
        return err;
    }

    @Override
    public BaseNode convert (ExpressionStatement node) {
        com.sencha.tools.compiler.ast.js.ExpressionStatement stmt =
            new com.sencha.tools.compiler.ast.js.ExpressionStatement();
        stmt.setExpression(doConvert(node.getExpression(), stmt));
        return stmt;
    }

    @Override
    public BaseNode convert (FunctionCall node) {
        com.sencha.tools.compiler.ast.js.FunctionCall call =
            new com.sencha.tools.compiler.ast.js.FunctionCall();
        call.setTarget(doConvert(node.getTarget(), call));
        for(AstNode arg : node.getArguments()) {
            call.addArgument(doConvert(arg, call));
        }
        return call;
    }

    @Override
    public BaseNode convert (IfStatement node) {
        com.sencha.tools.compiler.ast.js.IfStatement stmt =
            new com.sencha.tools.compiler.ast.js.IfStatement();
        stmt.setCondition(doConvert(node.getCondition(), stmt));
        stmt.setThen(doConvert(node.getThenPart(), stmt));
        stmt.setElse(doConvert(node.getElsePart(), stmt));
        return stmt;
    }

    @Override
    public BaseNode convert (InfixExpression node) {
        Infix infix = new Infix();
        return convertInfix(infix, node);
    }

    @Override
    public BaseNode convert (Jump node) {
        JumpNode jump = new JumpNode();
        return jump;
    }

    @Override
    public BaseNode convert (KeywordLiteral node) {
        com.sencha.tools.compiler.ast.js.KeywordLiteral lit =
            new com.sencha.tools.compiler.ast.js.KeywordLiteral();
        lit.setCode(node.getType());
        return lit;
    }

    @Override
    public BaseNode convert (LabeledStatement node) {
        com.sencha.tools.compiler.ast.js.LabeledStatement lbl =
            new com.sencha.tools.compiler.ast.js.LabeledStatement();
        for(Label l : node.getLabels()) {
            lbl.addLabel((com.sencha.tools.compiler.ast.js.Label) doConvert(l, lbl));
        }
        lbl.setStatement(doConvert(node.getStatement(), lbl));
        return lbl;
    }

    @Override
    public BaseNode convert (Name node) {
        com.sencha.tools.compiler.ast.js.Name name =
            new com.sencha.tools.compiler.ast.js.Name();
        name.setValue(node.getIdentifier());
        return name;
    }

    @Override
    public BaseNode convert (NumberLiteral node) {
        com.sencha.tools.compiler.ast.js.NumberLiteral num =
            new com.sencha.tools.compiler.ast.js.NumberLiteral();
        num.setValue(node.getNumber());
        return num;
    }

    @Override
    public BaseNode convert (ObjectLiteral node) {
        com.sencha.tools.compiler.ast.js.ObjectLiteral obj =
            new com.sencha.tools.compiler.ast.js.ObjectLiteral();
        for(ObjectProperty prop : node.getElements()) {
            obj.addElement((com.sencha.tools.compiler.ast.js.ObjectProperty) doConvert(prop, obj));
        }
        return obj;
    }

    @Override
    public BaseNode convert (ObjectProperty node) {
        com.sencha.tools.compiler.ast.js.ObjectProperty prop =
            new com.sencha.tools.compiler.ast.js.ObjectProperty();
        return convertInfix(prop, node);
    }

    @Override
    public BaseNode convert (ParenthesizedExpression node) {
        com.sencha.tools.compiler.ast.js.ParenthesizedExpression paren =
            new com.sencha.tools.compiler.ast.js.ParenthesizedExpression();
        paren.setExpr(doConvert(node.getExpression(), paren));
        return paren;
    }

    @Override
    public BaseNode convert (RegExpLiteral node) {
        com.sencha.tools.compiler.ast.js.RegExpLiteral lit =
            new com.sencha.tools.compiler.ast.js.RegExpLiteral();
        lit.setValue(node.getValue());
        lit.setFlags(node.getFlags());
        return lit;
    }

    @Override
    public BaseNode convert (ReturnStatement node) {
        com.sencha.tools.compiler.ast.js.ReturnStatement ret =
            new com.sencha.tools.compiler.ast.js.ReturnStatement();
        ret.setReturnValue(doConvert(node.getReturnValue(), ret));
        return ret;
    }

    @Override
    public BaseNode convert (StringLiteral node) {
        com.sencha.tools.compiler.ast.js.StringLiteral lit =
            new com.sencha.tools.compiler.ast.js.StringLiteral();
        lit.setValue(node.getValue());
        lit.setQuoteCharacter(node.getQuoteCharacter());
        return lit;
    }

    @Override
    public BaseNode convert (SwitchCase node) {
        com.sencha.tools.compiler.ast.js.SwitchCase sc =
            new com.sencha.tools.compiler.ast.js.SwitchCase();
        sc.setExpression(doConvert(node.getExpression(), sc));
        if(node.getStatements() != null) {
            for(AstNode child : node.getStatements()) {
                sc.addElement(doConvert(child, sc));
            }
        }
        return sc;
    }

    @Override
    public BaseNode convert (ThrowStatement node) {
        com.sencha.tools.compiler.ast.js.ThrowStatement th =
            new com.sencha.tools.compiler.ast.js.ThrowStatement();
        th.setExpr(doConvert(node.getExpression(), th));
        return th;
    }

    @Override
    public BaseNode convert (TryStatement node) {
        com.sencha.tools.compiler.ast.js.TryStatement stmt =
            new com.sencha.tools.compiler.ast.js.TryStatement();
        stmt.setTryBlock(doConvert(node.getTryBlock(), stmt));
        for(AstNode c : node.getCatchClauses()) {
            stmt.addCatchClause(doConvert(c, stmt));
        }
        stmt.setFinallyBlock(doConvert(node.getFinallyBlock(), stmt));
        return stmt;
    }

    @Override
    public BaseNode convert (UnaryExpression node) {
        Unary unary = new Unary();
        unary.setOperand(doConvert(node.getOperand(), unary));
        unary.setOperator(Operators.getByCode(node.getOperator()));
        unary.setPostfix(node.isPostfix());
        
        return unary;
    }

    @Override
    public BaseNode convert (VariableDeclaration node) {
        com.sencha.tools.compiler.ast.js.VariableDeclaration decl =
            new com.sencha.tools.compiler.ast.js.VariableDeclaration();
        for(AstNode var : node.getVariables()) {
            decl.addVariable((com.sencha.tools.compiler.ast.js.VariableInitializer) doConvert(var, decl));
        }
        decl.setStatement(node.isStatement());
        return decl;
    }

    @Override
    public BaseNode convert (VariableInitializer node) {
        com.sencha.tools.compiler.ast.js.VariableInitializer init =
            new com.sencha.tools.compiler.ast.js.VariableInitializer();
        init.setTarget(doConvert(node.getTarget(), init));
        init.setInitializer(doConvert(node.getInitializer(), init));
        return init;
    }

    @Override
    public BaseNode convert (WithStatement node) {
        com.sencha.tools.compiler.ast.js.WithStatement w =
            new com.sencha.tools.compiler.ast.js.WithStatement();
        w.setExpr(doConvert(node.getExpression(), w));
        w.setStatement(doConvert(node.getStatement(), w));
        return w;
    }

    @Override
    public BaseNode convert (XmlFragment node) {
        com.sencha.tools.compiler.ast.js.XmlFragment f =
            new com.sencha.tools.compiler.ast.js.XmlFragment();
        return f;
    }

    @Override
    public BaseNode convert (XmlLiteral node) {
        com.sencha.tools.compiler.ast.js.XmlLiteral lit =
            new com.sencha.tools.compiler.ast.js.XmlLiteral();
        for(AstNode f : node.getFragments()) {
            lit.addElement((com.sencha.tools.compiler.ast.js.XmlFragment) doConvert(f, lit));
        }
        return lit;
    }

    @Override
    public BaseNode convert (XmlRef node) {
        com.sencha.tools.compiler.ast.js.XmlRef r =
            new com.sencha.tools.compiler.ast.js.XmlRef();
        r.setNamespace((com.sencha.tools.compiler.ast.js.Name) doConvert(node.getNamespace(), r));
        return r;
    }

    @Override
    public BaseNode convert (Yield node) {
        YieldStatement y = new YieldStatement();
        y.setValue(doConvert(node.getValue(), y));
        return y;
    }

    @Override
    public BaseNode convert (Scope node) {
        com.sencha.tools.compiler.ast.js.Scope scope =
            new com.sencha.tools.compiler.ast.js.Scope();
        for(Node child : node) {
            scope.addElement(doConvert((AstNode) child, scope));
        }
        return scope;
    }

    @Override
    public BaseNode convert (ScriptNode node) {
        com.sencha.tools.compiler.ast.js.ScriptNode script =
            new com.sencha.tools.compiler.ast.js.ScriptNode();
        for(Node child : node) {
            script.addElement(doConvert((AstNode) child, script));
        }
        return script;
    }

    @Override
    public BaseNode convert (FunctionNode node) {
        com.sencha.tools.compiler.ast.js.FunctionNode func =
            new com.sencha.tools.compiler.ast.js.FunctionNode();
        func.setName((com.sencha.tools.compiler.ast.js.Name) doConvert(node.getFunctionName(), func));
        for(AstNode param : node.getParams()) {
            func.addParam(doConvert(param, func));
        }
        func.setBody(doConvert(node.getBody(), func));
        func.setExpressionClosure(node.isExpressionClosure());
        func.setMemberExpr(doConvert(node.getMemberExprNode(), func));
        return func;
    }

    @Override
    public BaseNode convert (BreakStatement node) {
        com.sencha.tools.compiler.ast.js.BreakStatement brk =
            new com.sencha.tools.compiler.ast.js.BreakStatement();
        brk.setLabel((com.sencha.tools.compiler.ast.js.Name) doConvert(node.getBreakLabel(), brk));
        return brk;
    }

    @Override
    public BaseNode convert (ContinueStatement node) {
        com.sencha.tools.compiler.ast.js.ContinueStatement stmt =
            new com.sencha.tools.compiler.ast.js.ContinueStatement();
        stmt.setLabel(doConvert(node.getLabel(), stmt));
        return stmt;
    }

    @Override
    public BaseNode convert (Label node) {
        com.sencha.tools.compiler.ast.js.Label lbl =
            new com.sencha.tools.compiler.ast.js.Label();
        lbl.setName(node.getName());
        return lbl;
    }

    @Override
    public BaseNode convert (SwitchStatement node) {
        com.sencha.tools.compiler.ast.js.SwitchStatement stmt =
            new com.sencha.tools.compiler.ast.js.SwitchStatement();
        stmt.setExpr(doConvert(node.getExpression(), stmt));
        for(AstNode c : node.getCases()) {
            stmt.addElement((com.sencha.tools.compiler.ast.js.SwitchCase) doConvert(c, stmt));
        }
        return stmt;
    }

    @Override
    public BaseNode convert (ArrayComprehension node) {
        com.sencha.tools.compiler.ast.js.ArrayComprehension comp =
            new com.sencha.tools.compiler.ast.js.ArrayComprehension();
        comp.setResult(doConvert(node.getResult(), comp));
        for(AstNode loop : node.getLoops()) {
            comp.addLoop(doConvert(loop, comp));
        }
        comp.setFilter(doConvert(node.getFilter(), comp));
        return comp;
    }

    @Override
    public BaseNode convert (LetNode node) {
        com.sencha.tools.compiler.ast.js.LetNode l =
            new com.sencha.tools.compiler.ast.js.LetNode();
        l.setVariables((com.sencha.tools.compiler.ast.js.VariableInitializer) doConvert(node.getVariables(), l));
        l.setBody(doConvert(node.getBody(), l));
        return l;
    }

    @Override
    public BaseNode convert (Loop node) {
        com.sencha.tools.compiler.ast.js.Loop loop =
            new com.sencha.tools.compiler.ast.js.Loop();
        loop.setBody(doConvert(node.getBody(), loop));
        return loop;
    }

    @Override
    public BaseNode convert (DoLoop node) {
        com.sencha.tools.compiler.ast.js.DoLoop dl =
            new com.sencha.tools.compiler.ast.js.DoLoop();
        dl.setCondition(doConvert(node.getCondition(), dl));
        dl.setBody(doConvert(node.getBody(), dl));
        return dl;
    }

    @Override
    public BaseNode convert (ForLoop node) {
        com.sencha.tools.compiler.ast.js.ForLoop fl =
            new com.sencha.tools.compiler.ast.js.ForLoop();
        fl.setInitializer(doConvert(node.getInitializer(), fl));
        fl.setCondition(doConvert(node.getCondition(), fl));
        fl.setIncrement(doConvert(node.getIncrement(), fl));
        fl.setBody(doConvert(node.getBody(), fl));
        return fl;
    }

    @Override
    public BaseNode convert (ForInLoop node) {
        com.sencha.tools.compiler.ast.js.ForInLoop fl =
            new com.sencha.tools.compiler.ast.js.ForInLoop();
        fl.setForEach(node.isForEach());
        fl.setIterator(doConvert(node.getIterator(), fl));
        fl.setIteratedObject(doConvert(node.getIteratedObject(), fl));
        fl.setBody(doConvert(node.getBody(), fl));
        return fl;
    }

    @Override
    public BaseNode convert (WhileLoop node) {
        com.sencha.tools.compiler.ast.js.WhileLoop wl =
            new com.sencha.tools.compiler.ast.js.WhileLoop();
        wl.setCondition(doConvert(node.getCondition(), wl));
        wl.setBody(doConvert(node.getBody(), wl));
        return wl;
    }

    @Override
    public BaseNode convert (ArrayComprehensionLoop node) {
        com.sencha.tools.compiler.ast.js.ArrayComprehensionLoop ac =
            new com.sencha.tools.compiler.ast.js.ArrayComprehensionLoop();
        ac.setIterator(doConvert(node.getIterator(), ac));
        ac.setIteratedObject(doConvert(node.getIteratedObject(), ac));
        ac.setBody(doConvert(node.getBody(), ac));
        return ac;
    }

    @Override
    public BaseNode convert (NewExpression node) {
        com.sencha.tools.compiler.ast.js.NewExpression n =
            new com.sencha.tools.compiler.ast.js.NewExpression();
        n.setTarget(doConvert(node.getTarget(), n));
        for(AstNode arg : node.getArguments()) {
            n.addArgument(doConvert(arg, n));
        }
        n.setInitializer((com.sencha.tools.compiler.ast.js.ObjectLiteral) doConvert(node.getInitializer(), n));
        return n;
    }

    @Override
    public BaseNode convert (EmptyStatement node) {
        return new com.sencha.tools.compiler.ast.js.EmptyExpression();
    }

    private List<CommentWrapper> _unclaimedComments = new ArrayList<CommentWrapper>();

    @Override
    public BaseNode convert (AstRoot node) {
        RootNode root = new RootNode();
        SortedSet<Comment> comments = node.getComments();
        _sourceName = node.getSourceName();

        if(comments != null && comments.size() > 0) {
            for(Comment c : comments) {
                CommentWrapper cw = new CommentWrapper();
                cw.setComment(c);
                cw.setAbsPosition(c.getAbsolutePosition());
                _unclaimedComments.add(cw);
            }
        }

        for(Node child : node) {
            root.addElement(doConvert((AstNode) child, root));
        }

        for(CommentWrapper comment : _unclaimedComments) {
            root.addComment((com.sencha.tools.compiler.ast.js.Comment) doConvert(comment.getComment(), null));
        }
        _unclaimedComments.clear();
        return root;
    }

    int _absolutePosition = 0;

    @Override
    public BaseNode doConvert (final AstNode node, BaseNode parent) {
        if(node == null) {
            return null;
        }

        int positionWas = _absolutePosition;
        int absPosition = positionWas + node.getPosition();
        _absolutePosition = absPosition;

        List<CommentWrapper> comments = new LinkedList<CommentWrapper>();
        if(!(node instanceof Comment)) {
            while (!_unclaimedComments.isEmpty() &&
                    _absolutePosition > _unclaimedComments.get(0).getAbsPosition() &&
                    !(node instanceof ParenthesizedExpression)) {
                comments.add(_unclaimedComments.remove(0));
            }
        }

        RhinoNodeConverter converter = getConverter(node);
        BaseNode baseNode = converter.convert(node, this);
        _absolutePosition = positionWas;

        if(baseNode != null) {
            baseNode.setParent(parent);
            baseNode.setFileName(_sourceName);
            baseNode.setLine(node.getLineno());
            baseNode.setOffset(node.getPosition());
            baseNode.setPosition(absPosition);
            for(CommentWrapper comment : comments) {
                baseNode.addComment(
                    (com.sencha.tools.compiler.ast.js.Comment) doConvert(comment.getComment(), null));
            }
        } else {
            _unclaimedComments.addAll(0, comments);
        }
        return baseNode;
    }

    private class CommentWrapper {
        private Comment _comment;
        private int _absPosition;

        public Comment getComment() {
            return _comment;
        }

        public void setComment(Comment comment) {
            _comment = comment;
        }

        public int getAbsPosition() {
            return _absPosition;
        }

        public void setAbsPosition(int absPosition) {
            _absPosition = absPosition;
        }
    }
}
