/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.util.http;

import com.sencha.util.FileInfo;
import com.sencha.util.StreamUtil;
import com.sencha.util.ZipUtil;
import jakarta.servlet.ServletContext;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;

public class FileOutputFormatter implements OutputFormatter {

    private File _file;
    private long _length;
    private boolean _enableMimeType;
    private String _mimeType = "application/octet-stream";

    public FileOutputFormatter(File file) {
        _file = file;
        _length = _file.length();
    }

    public FileOutputFormatter enableMimeType(boolean enable) {
        _enableMimeType = enable;
        return this;
    }

    public FileOutputFormatter setMimeType(String mimeType) {
        _mimeType = mimeType;
        return this;
    }

    public FileOutputFormatter detectMimeType(ServletContext context) {
        setMimeType(context.getMimeType(_file.getName()));
        return this;
    }

    @Override
    public String getMimeType() {
        if(_enableMimeType) {
            return _mimeType;
        }
        return null;
    }

    @Override
    public Wrapper wrap(Object content) {
        return new BinaryWrapper() {

            private void writeFileContent(OutputStream output) {
                StreamUtil.copyAll(
                    StreamUtil.openFileInput(_file),
                    output,
                    StreamUtil.Close.INPUT);
            }

            private void writeZipContent(OutputStream output) {
                FileInfo fi = new FileInfo(_file);
                ZipUtil.zip(output, fi.getAllZipSources());
            }

            @Override
            public void writeTo(OutputStream output) throws IOException {
                if(_file.isFile()) {
                    writeFileContent(output);
                } else {
                    writeZipContent(output);
                }
            }

            @Override
            public int getSize() {
                if(_file.isFile()) {
                    return (int) _file.length();
                }
                return 0;
            }
        };
    }
}
