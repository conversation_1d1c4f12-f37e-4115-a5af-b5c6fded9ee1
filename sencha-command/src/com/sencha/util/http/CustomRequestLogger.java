/*
 * Copyright (c) 2012-2024. Sencha Inc.
 */

package com.sencha.util.http;

import java.io.OutputStream;
import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodHandles;
import java.lang.invoke.MethodType;
import java.security.Principal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.function.BiPredicate;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.eclipse.jetty.http.HttpCookie;
import org.eclipse.jetty.http.HttpFields;
import org.eclipse.jetty.http.HttpURI;
import org.eclipse.jetty.http.QuotedCSV;
import org.eclipse.jetty.http.pathmap.PathMappings;
import org.eclipse.jetty.server.*;
import org.eclipse.jetty.util.*;
import org.eclipse.jetty.util.annotation.ManagedAttribute;
import org.eclipse.jetty.util.annotation.ManagedObject;
import org.eclipse.jetty.util.component.ContainerLifeCycle;
import org.eclipse.jetty.util.resource.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ManagedObject("Custom format request log")
public class CustomRequestLogger extends ContainerLifeCycle implements RequestLog {
    public static final String DEFAULT_DATE_FORMAT = "dd/MMM/yyyy:HH:mm:ss ZZZ";
    public static final String NCSA_FORMAT = "%{client}a - %u %t \"%r\" %s %O";
    public static final String EXTENDED_NCSA_FORMAT = "%{client}a - %u %t \"%r\" %s %O \"%{Referer}i\" \"%{User-Agent}i\"";
    public static final String LOG_DETAIL = CustomRequestLogger.class.getName() + ".logDetail";
    private static final Logger LOG = LoggerFactory.getLogger(CustomRequestLogger.class);
    private static final ThreadLocal<StringBuilder> _buffers = ThreadLocal.withInitial(() -> {
        return new StringBuilder(256);
    });
    private static final Pattern PATTERN = Pattern.compile("^(?:%(?<MOD>!?[0-9,]+)?(?:\\{(?<ARG>[^}]+)})?(?<CODE>(?:(?:ti)|(?:to)|(?:uri)|(?:attr)|[a-zA-Z%]))|(?<LITERAL>[^%]+))(?<REMAINING>.*)", 40);
    private final RequestLog.Writer _requestLogWriter;
    private final MethodHandle _logHandle;
    private final String _formatString;
    private transient PathMappings<String> _ignorePathMap;
    private String[] _ignorePaths;
    private BiPredicate<Request, Response> _filter;
    private Pattern _whiteList;

    private String _filename;


    private transient OutputStream _out;
    private transient OutputStream _fileOut;
    private transient DateCache _logDateCache;

    public CustomRequestLogger() {
        this((RequestLog.Writer)(new Slf4jRequestLogWriter()), "%{client}a - %u %t \"%r\" %s %O \"%{Referer}i\" \"%{User-Agent}i\"");
    }

    public CustomRequestLogger(String file) {
        this(file, "%{client}a - %u %t \"%r\" %s %O \"%{Referer}i\" \"%{User-Agent}i\"");
    }

    public CustomRequestLogger(String file, String format) {
        this((RequestLog.Writer)(new RequestLogWriter(file)), format);
    }

    public CustomRequestLogger(RequestLog.Writer writer, String formatString) {
        this._formatString = formatString;
        this._requestLogWriter = writer;
        this.installBean(this._requestLogWriter);

        try {
            this._logHandle = this.getLogHandle(formatString);
        } catch (IllegalAccessException | NoSuchMethodException var4) {
            ReflectiveOperationException e = var4;
            throw new IllegalStateException(e);
        }
    }

    public void setFilter(BiPredicate<Request, Response> filter) {
        this._filter = filter;
    }

    public Pattern getWhiteList () {
        return _whiteList;
    }
    public void setWhiteList (Pattern regex) {
        _whiteList = regex;
    }
    public void setWhiteList (String regex) {
        setWhiteList(Pattern.compile(regex));
    }

    @ManagedAttribute("The RequestLogWriter")
    public RequestLog.Writer getWriter() {
        return this._requestLogWriter;
    }

    public void log(Request request, Response response) {
        if (!isStarted())
            return;

        try {
            String url = request.getHttpURI().asString();
            if (_whiteList != null && !_whiteList.matcher(url).matches()) {
                return;
            }

            if (this._ignorePathMap != null && this._ignorePathMap.getMatched(request.getHttpURI().getCanonicalPath()) != null) {
                return;
            }

            if (this._filter != null && !this._filter.test(request, response)) {
                return;
            }

            StringBuilder sb = (StringBuilder)_buffers.get();
            sb.setLength(0);
            this._logHandle.invoke(sb, request, response);
            String log = sb.toString();
            this._requestLogWriter.write(log);
        } catch (Throwable var5) {
            Throwable e = var5;
            LOG.warn("Unable to log request", e);
        }

    }

    public void setIgnorePaths(String[] ignorePaths) {
        this._ignorePaths = ignorePaths;
    }

    public String[] getIgnorePaths() {
        return this._ignorePaths;
    }

    @ManagedAttribute("format string")
    public String getFormatString() {
        return this._formatString;
    }

    public void setFilename(String filename)
    {
        if (filename != null)
        {
            filename = filename.trim();
            if (filename.length() == 0)
                filename = null;
        }
        _filename = filename;
    }

    public String getFilename()
    {
        return _filename;
    }

    public String getDatedFilename()
    {
        if (_fileOut instanceof RolloverFileOutputStream)
            return ((RolloverFileOutputStream)_fileOut).getDatedFilename();
        return null;
    }

    protected void doStart() throws Exception {
        if (this._ignorePaths != null && this._ignorePaths.length > 0) {
            this._ignorePathMap = new PathMappings();
            String[] ignorePaths = this._ignorePaths;
            int pathLength = ignorePaths.length;

            for(int i = 0; i < pathLength; ++i) {
                String ignorePath = ignorePaths[i];
                this._ignorePathMap.put(ignorePath, ignorePath);
            }
        } else {
            this._ignorePathMap = null;
        }

        super.doStart();
    }

    private static void append(StringBuilder buf, String s) {
        if (s != null && !s.isEmpty()) {
            buf.append(s);
        } else {
            buf.append('-');
        }

    }

    private static void append(String s, StringBuilder buf) {
        append(buf, s);
    }

    private MethodHandle getLogHandle(String formatString) throws NoSuchMethodException, IllegalAccessException {
        MethodHandles.Lookup lookup = MethodHandles.lookup();
        MethodHandle append = lookup.findStatic(CustomRequestLogger.class, "append", MethodType.methodType(Void.TYPE, String.class, StringBuilder.class));
        MethodHandle logHandle = lookup.findStatic(CustomRequestLogger.class, "logNothing", MethodType.methodType(Void.TYPE, StringBuilder.class, Request.class, Response.class));
        List<CustomRequestLogger.Token> tokens = getTokens(formatString);
        Collections.reverse(tokens);
        Iterator tokenItr = tokens.iterator();

        while(tokenItr.hasNext()) {
            CustomRequestLogger.Token t = (CustomRequestLogger.Token)tokenItr.next();
            if (t.isLiteralString()) {
                logHandle = this.updateLogHandle(logHandle, append, t.literal);
            } else {
                if (!t.isPercentCode()) {
                    throw new IllegalStateException("bad token " + String.valueOf(t));
                }

                logHandle = this.updateLogHandle(logHandle, append, lookup, t.code, t.arg, t.modifiers, t.negated);
            }
        }

        return logHandle;
    }

    private static List<CustomRequestLogger.Token> getTokens(String formatString) {
        List<CustomRequestLogger.Token> tokens = new ArrayList();

        Matcher m;
        for(String remaining = formatString; remaining.length() > 0; remaining = m.group("REMAINING")) {
            m = PATTERN.matcher(remaining);
            if (!m.matches()) {
                throw new IllegalArgumentException("Invalid format string: " + formatString);
            }

            String code;
            if (m.group("CODE") != null) {
                code = m.group("CODE");
                String arg = m.group("ARG");
                String modifierString = m.group("MOD");
                List<Integer> modifiers = null;
                boolean negated = false;
                if (modifierString != null) {
                    if (modifierString.startsWith("!")) {
                        modifierString = modifierString.substring(1);
                        negated = true;
                    }

                    modifiers = (List)(new QuotedCSV(new String[]{modifierString})).getValues().stream().map(Integer::parseInt).collect(Collectors.toList());
                }

                tokens.add(new CustomRequestLogger.Token(code, arg, modifiers, negated));
            } else {
                if (m.group("LITERAL") == null) {
                    throw new IllegalStateException("formatString parsing error: " + formatString);
                }

                code = m.group("LITERAL");
                tokens.add(new CustomRequestLogger.Token(code));
            }
        }

        return tokens;
    }

    private static boolean modify(List<Integer> modifiers, Boolean negated, StringBuilder b, Request request, Response response) {
        if (negated) {
            return !modifiers.contains(response.getStatus());
        } else {
            return modifiers.contains(response.getStatus());
        }
    }

    private MethodHandle updateLogHandle(MethodHandle logHandle, MethodHandle append, String literal) {
        return MethodHandles.foldArguments(logHandle, MethodHandles.dropArguments(MethodHandles.dropArguments(append.bindTo(literal), 1, new Class[]{Request.class}), 2, new Class[]{Response.class}));
    }

    private MethodHandle updateLogHandle(MethodHandle logHandle, MethodHandle append, MethodHandles.Lookup lookup, String code, String arg, List<Integer> modifiers, boolean negated) throws NoSuchMethodException, IllegalAccessException {
        MethodType logType = MethodType.methodType(Void.TYPE, StringBuilder.class, Request.class, Response.class);
        MethodType logTypeArg = MethodType.methodType(Void.TYPE, String.class, StringBuilder.class, Request.class, Response.class);
        MethodHandle methodHandle;
        String method;
        String methodName;
        switch (code) {
            case "%":
                methodHandle = MethodHandles.dropArguments(MethodHandles.dropArguments(append.bindTo("%"), 1, new Class[]{Request.class}), 2, new Class[]{Response.class});
                break;
            case "a":
                if (StringUtil.isEmpty(arg)) {
                    arg = "server";
                }

                switch (arg) {
                    case "server" -> methodName = "logServerHost";
                    case "client" -> methodName = "logClientHost";
                    case "local" -> methodName = "logLocalHost";
                    case "remote" -> methodName = "logRemoteHost";
                    default -> throw new IllegalArgumentException("Invalid arg for %a");
                }

                method = methodName;
                methodHandle = lookup.findStatic(CustomRequestLogger.class, method, logType);
                break;
            case "p":
                if (StringUtil.isEmpty(arg)) {
                    arg = "server";
                }

                switch (arg) {
                    case "server" -> methodName = "logServerPort";
                    case "client" -> methodName = "logClientPort";
                    case "local" -> methodName = "logLocalPort";
                    case "remote" -> methodName = "logRemotePort";
                    default -> throw new IllegalArgumentException("Invalid arg for %p");
                }

                method = methodName;
                methodHandle = lookup.findStatic(CustomRequestLogger.class, method, logType);
                break;
            case "I":
                if (StringUtil.isEmpty(arg)) {
                    method = "logBytesReceived";
                } else {
                    if (!arg.equalsIgnoreCase("clf")) {
                        throw new IllegalArgumentException("Invalid argument for %I");
                    }

                    method = "logBytesReceivedCLF";
                }

                methodHandle = lookup.findStatic(CustomRequestLogger.class, method, logType);
                break;
            case "O":
                if (StringUtil.isEmpty(arg)) {
                    method = "logBytesSent";
                } else {
                    if (!arg.equalsIgnoreCase("clf")) {
                        throw new IllegalArgumentException("Invalid argument for %O");
                    }

                    method = "logBytesSentCLF";
                }

                methodHandle = lookup.findStatic(CustomRequestLogger.class, method, logType);
                break;
            case "S":
                if (StringUtil.isEmpty(arg)) {
                    method = "logBytesTransferred";
                } else {
                    if (!arg.equalsIgnoreCase("clf")) {
                        throw new IllegalArgumentException("Invalid argument for %S");
                    }

                    method = "logBytesTransferredCLF";
                }

                methodHandle = lookup.findStatic(CustomRequestLogger.class, method, logType);
                break;
            case "C":
                methodHandle = StringUtil.isEmpty(arg) ? lookup.findStatic(CustomRequestLogger.class, "logRequestCookies", logType) : lookup.findStatic(CustomRequestLogger.class, "logRequestCookie", logTypeArg).bindTo(arg);
                break;
            case "D":
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logLatencyMicroseconds", logType);
                break;
            case "e":
                if (StringUtil.isEmpty(arg)) {
                    throw new IllegalArgumentException("No arg for %e");
                }

                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logEnvironmentVar", logTypeArg).bindTo(arg);
                break;
            case "f":
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logFilename", logType);
                break;
            case "H":
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logRequestProtocol", logType);
                break;
            case "i":
                if (StringUtil.isEmpty(arg)) {
                    throw new IllegalArgumentException("No arg for %i");
                }

                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logRequestHeader", logTypeArg).bindTo(arg);
                break;
            case "k":
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logKeepAliveRequests", logType);
                break;
            case "m":
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logRequestMethod", logType);
                break;
            case "o":
                if (StringUtil.isEmpty(arg)) {
                    throw new IllegalArgumentException("No arg for %o");
                }

                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logResponseHeader", logTypeArg).bindTo(arg);
                break;
            case "q":
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logQueryString", logType);
                break;
            case "r":
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logRequestFirstLine", logType);
                break;
            case "R":
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logRequestHandler", logType);
                break;
            case "s":
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logResponseStatus", logType);
                break;
            case "t":
                method = "dd/MMM/yyyy:HH:mm:ss ZZZ";
                TimeZone timeZone = TimeZone.getTimeZone("GMT");
                Locale locale = Locale.getDefault();
                if (arg != null && !arg.isEmpty()) {
                    String[] args = arg.split("\\|");
                    switch (args.length) {
                        case 1:
                            method = args[0];
                            break;
                        case 2:
                            method = args[0];
                            timeZone = TimeZone.getTimeZone(args[1]);
                            break;
                        case 3:
                            method = args[0];
                            timeZone = TimeZone.getTimeZone(args[1]);
                            locale = Locale.forLanguageTag(args[2]);
                            break;
                        default:
                            throw new IllegalArgumentException("Too many \"|\" characters in %t");
                    }
                }

                DateCache logDateCache = new DateCache(method, locale, timeZone);
                MethodType logTypeDateCache = MethodType.methodType(Void.TYPE, DateCache.class, StringBuilder.class, Request.class, Response.class);
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logRequestTime", logTypeDateCache).bindTo(logDateCache);
                break;
            case "T":
                if (arg == null) {
                    arg = "s";
                }

                switch (arg) {
                    case "s" -> methodName = "logLatencySeconds";
                    case "us" -> methodName = "logLatencyMicroseconds";
                    case "ms" -> methodName = "logLatencyMilliseconds";
                    default -> throw new IllegalArgumentException("Invalid arg for %T");
                }

                method = methodName;
                methodHandle = lookup.findStatic(CustomRequestLogger.class, method, logType);
                break;
            case "u":
                if (StringUtil.isEmpty(arg)) {
                    method = "logRequestAuthentication";
                } else {
                    if (!"d".equals(arg)) {
                        throw new IllegalArgumentException("Invalid arg for %u: " + arg);
                    }

                    method = "logRequestAuthenticationWithDeferred";
                }

                methodHandle = lookup.findStatic(CustomRequestLogger.class, method, logType);
                break;
            case "U":
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logUrlRequestPath", logType);
                break;
            case "X":
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logConnectionStatus", logType);
                break;
            case "ti":
                if (StringUtil.isEmpty(arg)) {
                    throw new IllegalArgumentException("No arg for %ti");
                }

                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logRequestTrailer", logTypeArg).bindTo(arg);
                break;
            case "to":
                if (StringUtil.isEmpty(arg)) {
                    throw new IllegalArgumentException("No arg for %to");
                }

                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logResponseTrailer", logTypeArg).bindTo(arg);
                break;
            case "uri":
                if (arg == null) {
                    arg = "";
                }

                switch (arg) {
                    case "" -> methodName = "logRequestHttpUri";
                    case "-query" -> methodName = "logRequestHttpUriWithoutQuery";
                    case "-path,-query" -> methodName = "logRequestHttpUriWithoutPathQuery";
                    case "scheme" -> methodName = "logRequestScheme";
                    case "authority" -> methodName = "logRequestAuthority";
                    case "path" -> methodName = "logUrlRequestPath";
                    case "query" -> methodName = "logQueryString";
                    case "host" -> methodName = "logRequestHttpUriHost";
                    case "port" -> methodName = "logRequestHttpUriPort";
                    default -> throw new IllegalArgumentException("Invalid arg for %uri");
                }

                method = methodName;
                methodHandle = lookup.findStatic(CustomRequestLogger.class, method, logType);
                break;
            case "attr":
                MethodType logRequestAttribute = MethodType.methodType(Void.TYPE, String.class, StringBuilder.class, Request.class, Response.class);
                methodHandle = lookup.findStatic(CustomRequestLogger.class, "logRequestAttribute", logRequestAttribute).bindTo(arg);
                break;
            default:
                throw new IllegalArgumentException("Unsupported code %" + code);
        }

        MethodHandle specificHandle = methodHandle;
        if (modifiers != null && !modifiers.isEmpty()) {
            MethodHandle dash = this.updateLogHandle(logHandle, append, "-");
            MethodHandle log = MethodHandles.foldArguments(logHandle, specificHandle);
            MethodHandle modifierTest = lookup.findStatic(CustomRequestLogger.class, "modify", MethodType.methodType(Boolean.TYPE, List.class, Boolean.class, StringBuilder.class, Request.class, Response.class));
            modifierTest = modifierTest.bindTo(modifiers).bindTo(negated);
            return MethodHandles.guardWithTest(modifierTest, log, dash);
        } else {
            return MethodHandles.foldArguments(logHandle, specificHandle);
        }
    }

    private static void logNothing(StringBuilder b, Request request, Response response) {
    }

    private static void logServerHost(StringBuilder b, Request request, Response response) {
        append(b, Request.getServerName(request));
    }

    private static void logClientHost(StringBuilder b, Request request, Response response) {
        append(b, Request.getRemoteAddr(request));
    }

    private static void logLocalHost(StringBuilder b, Request request, Response response) {
        append(b, Request.getLocalAddr(Request.unWrap(request)));
    }

    private static void logRemoteHost(StringBuilder b, Request request, Response response) {
        append(b, Request.getRemoteAddr(Request.unWrap(request)));
    }

    private static void logServerPort(StringBuilder b, Request request, Response response) {
        b.append(Request.getServerPort(request));
    }

    private static void logClientPort(StringBuilder b, Request request, Response response) {
        b.append(Request.getRemotePort(request));
    }

    private static void logLocalPort(StringBuilder b, Request request, Response response) {
        b.append(Request.getLocalPort(Request.unWrap(request)));
    }

    private static void logRemotePort(StringBuilder b, Request request, Response response) {
        b.append(Request.getRemotePort(Request.unWrap(request)));
    }

    private static void logResponseSize(StringBuilder b, Request request, Response response) {
        b.append(Response.getContentBytesWritten(response));
    }

    private static void logResponseSizeCLF(StringBuilder b, Request request, Response response) {
        long written = Response.getContentBytesWritten(response);
        if (written == 0L) {
            b.append('-');
        } else {
            b.append(written);
        }

    }

    private static void logBytesSent(StringBuilder b, Request request, Response response) {
        b.append(Response.getContentBytesWritten(response));
    }

    private static void logBytesSentCLF(StringBuilder b, Request request, Response response) {
        long sent = Response.getContentBytesWritten(response);
        if (sent == 0L) {
            b.append('-');
        } else {
            b.append(sent);
        }

    }

    private static void logBytesReceived(StringBuilder b, Request request, Response response) {
        b.append(Request.getContentBytesRead(request));
    }

    private static void logBytesReceivedCLF(StringBuilder b, Request request, Response response) {
        long received = Request.getContentBytesRead(request);
        if (received == 0L) {
            b.append('-');
        } else {
            b.append(received);
        }

    }

    private static void logBytesTransferred(StringBuilder b, Request request, Response response) {
        b.append(Request.getContentBytesRead(request) + Response.getContentBytesWritten(response));
    }

    private static void logBytesTransferredCLF(StringBuilder b, Request request, Response response) {
        long transferred = Request.getContentBytesRead(request) + Response.getContentBytesWritten(response);
        if (transferred == 0L) {
            b.append('-');
        } else {
            b.append(transferred);
        }

    }

    private static void logRequestCookie(String arg, StringBuilder b, Request request, Response response) {
        List<HttpCookie> cookies = Request.getCookies(request);
        if (cookies != null) {
            Iterator cookieIterator = cookies.iterator();

            while(cookieIterator.hasNext()) {
                HttpCookie c = (HttpCookie)cookieIterator.next();
                if (arg.equals(c.getName())) {
                    b.append(c.getValue());
                    return;
                }
            }
        }

        b.append('-');
    }

    private static void logRequestCookies(StringBuilder b, Request request, Response response) {
        List<HttpCookie> cookies = Request.getCookies(request);
        if (cookies != null && cookies.size() != 0) {
            for(int i = 0; i < cookies.size(); ++i) {
                if (i != 0) {
                    b.append(';');
                }

                b.append(((HttpCookie)cookies.get(i)).getName());
                b.append('=');
                b.append(((HttpCookie)cookies.get(i)).getValue());
            }
        } else {
            b.append('-');
        }

    }

    private static void logEnvironmentVar(String arg, StringBuilder b, Request request, Response response) {
        append(b, System.getenv(arg));
    }

    private static void logFilename(StringBuilder b, Request request, Response response) {
        CustomRequestLogger.LogDetail logDetail = (CustomRequestLogger.LogDetail)request.getAttribute(LOG_DETAIL);
        if (logDetail != null && logDetail.realPath != null) {
            b.append(logDetail.realPath);
        } else {
            Context context = request.getContext();
            Resource baseResource = context.getBaseResource();
            if (baseResource != null) {
                String fileName = baseResource.resolve(Request.getPathInContext(request)).getName();
                append(b, fileName);
            } else {
                b.append("-");
            }
        }

    }

    private static void logRequestProtocol(StringBuilder b, Request request, Response response) {
        append(b, request.getConnectionMetaData().getProtocol());
    }

    private static void logRequestHeader(String arg, StringBuilder b, Request request, Response response) {
        append(b, request.getHeaders().get(arg));
    }

    private static void logKeepAliveRequests(StringBuilder b, Request request, Response response) {
        long requests = request.getConnectionMetaData().getConnection().getMessagesIn();
        if (requests >= 0L) {
            b.append(requests);
        } else {
            b.append('-');
        }

    }

    private static void logRequestMethod(StringBuilder b, Request request, Response response) {
        append(b, request.getMethod());
    }

    private static void logResponseHeader(String arg, StringBuilder b, Request request, Response response) {
        append(b, response.getHeaders().get(arg));
    }

    private static void logQueryString(StringBuilder b, Request request, Response response) {
        append(b, "?" + request.getHttpURI().getQuery());
    }

    private static void logRequestFirstLine(StringBuilder b, Request request, Response response) {
        append(b, request.getMethod());
        b.append(" ");
        append(b, request.getHttpURI().getPathQuery());
        b.append(" ");
        append(b, request.getConnectionMetaData().getProtocol());
    }

    private static void logRequestHandler(StringBuilder b, Request request, Response response) {
        CustomRequestLogger.LogDetail logDetail = (CustomRequestLogger.LogDetail)request.getAttribute(LOG_DETAIL);
        append(b, logDetail == null ? null : logDetail.handlerName);
    }

    private static void logResponseStatus(StringBuilder b, Request request, Response response) {
        b.append(response.getStatus());
    }

    private static void logRequestTime(DateCache dateCache, StringBuilder b, Request request, Response response) {
        b.append('[');
        append(b, dateCache.format(Request.getTimeStamp(request)));
        b.append(']');
    }

    private static void logLatencyMicroseconds(StringBuilder b, Request request, Response response) {
        logLatency(b, request, TimeUnit.MICROSECONDS);
    }

    private static void logLatencyMilliseconds(StringBuilder b, Request request, Response response) {
        logLatency(b, request, TimeUnit.MILLISECONDS);
    }

    private static void logLatencySeconds(StringBuilder b, Request request, Response response) {
        logLatency(b, request, TimeUnit.SECONDS);
    }

    private static void logLatency(StringBuilder b, Request request, TimeUnit unit) {
        b.append(unit.convert(NanoTime.since(request.getBeginNanoTime()), TimeUnit.NANOSECONDS));
    }

    private static void logRequestAuthentication(StringBuilder b, Request request, Response response) {
        Request.AuthenticationState authenticationState = Request.getAuthenticationState(request);
        Principal userPrincipal = authenticationState == null ? null : authenticationState.getUserPrincipal();
        append(b, userPrincipal == null ? null : userPrincipal.getName());
    }

    private static void logRequestAuthenticationWithDeferred(StringBuilder b, Request request, Response response) {
        logRequestAuthentication(b, request, response);
    }

    private static void logUrlRequestPath(StringBuilder b, Request request, Response response) {
        append(b, request.getHttpURI().getPath());
    }

    private static void logConnectionStatus(StringBuilder b, Request request, Response response) {
        b.append((char)(response.isCompletedSuccessfully() ? (request.getConnectionMetaData().isPersistent() ? '+' : '-') : 'X'));
    }

    private static void logRequestTrailer(String arg, StringBuilder b, Request request, Response response) {
        HttpFields trailers = request.getTrailers();
        if (trailers != null) {
            append(b, trailers.get(arg));
        } else {
            b.append('-');
        }

    }

    private static void logResponseTrailer(String arg, StringBuilder b, Request request, Response response) {
        Supplier<HttpFields> supplier = response.getTrailersSupplier();
        HttpFields trailers = supplier == null ? null : (HttpFields)supplier.get();
        if (trailers != null) {
            append(b, trailers.get(arg));
        } else {
            b.append('-');
        }

    }

    private static void logRequestAuthority(StringBuilder b, Request request, Response response) {
        HttpURI httpURI = request.getHttpURI();
        if (httpURI.hasAuthority()) {
            append(b, httpURI.getAuthority());
        } else {
            b.append('-');
        }

    }

    private static void logRequestScheme(StringBuilder b, Request request, Response response) {
        append(b, request.getHttpURI().getScheme());
    }

    private static void logRequestHttpUri(StringBuilder b, Request request, Response response) {
        append(b, request.getHttpURI().toString());
    }

    private static void logRequestHttpUriWithoutQuery(StringBuilder b, Request request, Response response) {
        HttpURI.Mutable uri = HttpURI.build(request.getHttpURI()).query((String)null);
        append(b, uri.toString());
    }

    private static void logRequestHttpUriWithoutPathQuery(StringBuilder b, Request request, Response response) {
        HttpURI httpURI = request.getHttpURI();
        if (httpURI.getScheme() != null) {
            b.append(httpURI.getScheme()).append(':');
        }

        if (httpURI.getHost() != null) {
            b.append("//");
            if (httpURI.getUser() != null) {
                b.append(httpURI.getUser()).append('@');
            }

            b.append(httpURI.getHost());
        }

        int normalizedPort = URIUtil.normalizePortForScheme(httpURI.getScheme(), httpURI.getPort());
        if (normalizedPort > 0) {
            b.append(':').append(normalizedPort);
        }

    }

    private static void logRequestHttpUriHost(StringBuilder b, Request request, Response response) {
        append(b, request.getHttpURI().getHost());
    }

    private static void logRequestHttpUriPort(StringBuilder b, Request request, Response response) {
        b.append(request.getHttpURI().getPort());
    }

    private static void logRequestAttribute(String arg, StringBuilder b, Request request, Response response) {
        Object attribute = request.getAttribute(arg);
        if (attribute != null) {
            append(b, attribute.toString());
        } else {
            b.append('-');
        }

    }

    private static class Token {
        public final String code;
        public final String arg;
        public final List<Integer> modifiers;
        public final boolean negated;
        public final String literal;

        public Token(String code, String arg, List<Integer> modifiers, boolean negated) {
            this.code = code;
            this.arg = arg;
            this.modifiers = modifiers;
            this.negated = negated;
            this.literal = null;
        }

        public Token(String literal) {
            this.code = null;
            this.arg = null;
            this.modifiers = null;
            this.negated = false;
            this.literal = literal;
        }

        public boolean isLiteralString() {
            return this.literal != null;
        }

        public boolean isPercentCode() {
            return this.code != null;
        }
    }

    public static record LogDetail(String handlerName, String realPath) {
        public LogDetail(String handlerName, String realPath) {
            this.handlerName = handlerName;
            this.realPath = realPath;
        }

        public String handlerName() {
            return this.handlerName;
        }

        public String realPath() {
            return this.realPath;
        }
    }
}
