/*
 * Copyright (c) 2013. Sencha Inc.
 */
package com.sencha.util.http;

import com.sencha.exceptions.BasicException;
import com.sencha.util.CollectionUtil;
import com.sencha.util.Converter;
import com.sencha.util.ObjectUtil;
import com.sencha.util.StringUtil;
import jakarta.servlet.ServletContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.io.Reader;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * This class dispatches incoming requests to child Responder instances or itself.
 */
public interface Responder {
    void attach (Responder parent, String name);

    /**
     * Dispatches based on the provided Context.
     * @param context Describes the request
     */
    void dispatch (Context context);

    String getName ();

    Responder getParent ();

    //-------------------------------------------------------------------------
    // Following are the many highly correlated definitions

    public static enum ContentFlow {
        REQUEST,
        RESPONSE,
        BOTH,
        NEITHER
        ;

        public boolean hasRequestBody () {
            return this == REQUEST || this == BOTH;
        }

        public boolean hasResponseBody () {
            return this == RESPONSE || this == BOTH;
        }
    }

    /**
     * This class wraps the HTTP action (e.g., "GET") and provides the metadata about the
     * flow of body content for the action.
     */
    public static final class Action {
        public static final Action GET;
        public static final Action PUT;
        public static final Action POST;
        public static final Action DELETE;

        public static Action create (String name, ContentFlow flow) {
            Action action = new Action(name, flow);

            synchronized (_actions) {
                _actions.put(name.toUpperCase(), action);
            }

            return action;
        }

        public static Action get (String name) {
            Action action;
            String upper = name.toUpperCase();

            synchronized (_actions) {
                action = _actions.get(upper);
            }

            if (action == null) {
                action = new Action(upper, ContentFlow.NEITHER);
            }

            return action;
        }

        public ContentFlow getFlow () {
            return _flow;
        }

        public String getName () {
            return _name;
        }

        private Action (String name, ContentFlow flow) {
            _flow = flow;
            _name = name;
        }

        private static final Map<String, Action> _actions;

        static {
            _actions = new HashMap();

            GET    = Action.create("GET",    ContentFlow.RESPONSE);
            PUT    = Action.create("PUT",    ContentFlow.REQUEST);
            POST   = Action.create("POST",   ContentFlow.BOTH);
            DELETE = Action.create("DELETE", ContentFlow.NEITHER);
        }

        private final ContentFlow _flow;
        private final String _name;
    }

    /**
     * An enumeration of HTTP status codes.
     */
    public static enum Status {
        CONTINUE(100),
        SWITCHING_PROTOCOLS(101),
        OK(200),
        CREATED(201),
        SC_ACCEPTED(202),
        SC_NON_AUTHORITATIVE_INFORMATION(203),
        SC_NO_CONTENT(204),
        SC_RESET_CONTENT(205),
        SC_PARTIAL_CONTENT(206),
        SC_MULTIPLE_CHOICES(300),
        SC_MOVED_PERMANENTLY(301),
        SC_MOVED_TEMPORARILY(302),
        FOUND(302),
        SEE_OTHER(303),
        NOT_MODIFIED(304),
        USE_PROXY(305),
        TEMPORARY_REDIRECT(307),
        BAD_REQUEST(400),
        UNAUTHORIZED(401),
        PAYMENT_REQUIRED(402),
        FORBIDDEN(403),
        NOT_FOUND(404),
        METHOD_NOT_ALLOWED(405),
        NOT_ACCEPTABLE(406),
        PROXY_AUTHENTICATION_REQUIRED(407),
        REQUEST_TIMEOUT(408),
        CONFLICT(409),
        GONE(410),
        LENGTH_REQUIRED(411),
        PRECONDITION_FAILED(412),
        REQUEST_ENTITY_TOO_LARGE(413),
        REQUEST_URI_TOO_LONG(414),
        UNSUPPORTED_MEDIA_TYPE(415),
        REQUESTED_RANGE_NOT_SATISFIABLE(416),
        EXPECTATION_FAILED(417),
        INTERNAL_SERVER_ERROR(500),
        NOT_IMPLEMENTED(501),
        BAD_GATEWAY(502),
        SERVICE_UNAVAILABLE(503),
        GATEWAY_TIMEOUT(504),
        HTTP_VERSION_NOT_SUPPORTED(505)
        ;

        public int getCode () {
            return _code;
        }

        Status (int code) {
            _code = code;
        }

        private final int _code;
    }

    /**
     * This class describes a resource being requested. Resources have a name and a format
     * (the file extension).
     */
    public static class Resource {
        public Resource (String name) {
            _name = name;
        }

        public String getName () {
            return _name;
        }

        /**
         * Returns the format part of the name. To support names that contain dots, we
         * must be given the base name beyond which is the format/type.
         */
        public String getFormat (String baseName) {
            int pos = baseName.length();
            if (pos < _name.length() && _name.charAt(pos) == '.') {
                ++pos;
            }
            String format = _name.substring(pos);
            return format;
        }

        private final String _name;
    }

    /**
     * This class encapsulates the request/response context to help remove dependency on
     * raw servlet API.
     * @param <T> The type of the content object.
     */
    public static class Context<T> {
        public Context (HttpServletRequest request, HttpServletResponse response) {
            _action = Action.get(request.getMethod());
            _rawRequest = request;
            _rawResponse = response;

            String pathInfo = request.getPathInfo();
            if (StringUtil.isNullOrEmpty(pathInfo) || "/".equals(pathInfo)) {
                _path = _emptyPath;
            } else {
                if (pathInfo.startsWith("/")) {
                    pathInfo = pathInfo.substring(1);
                }
                _path = pathInfo.split("/");
            }
        }

        public ServletContext getServletContext() {
            return _rawRequest.getServletContext();
        }

        public boolean atEnd () {
            return peekPath() == null;
        }

        public Action getAction () {
            return _action;
        }

        public void setAction (Action action) {
            _action = action;
        }

        public T getContent () {
            return _content;
        }

        public void setContent (T content) {
            _content = content;
        }

        public InputStream getRequestInputStream () {
            try {
                return _rawRequest.getInputStream();
            } catch (IOException ex) {
                throw BasicException.raise(ex);
            }
        }

        public Reader getRequestReader () {
            try {
                return _rawRequest.getReader();
            } catch (IOException ex) {
                throw BasicException.raise(ex);
            }
        }

        public String getCurrentPath () {
            peekPath();

            StringBuilder ret = new StringBuilder();
            ret.append('/');

            for (int i = 0; i < _nextPathIndex; ++i) {
                ret.append(_path[i]).append('/');
            }
            return ret.toString();
        }

        public OutputFormatter getOutputFormatter () {
            return _outputFormatter;
        }

        public void setOutputFormatter (OutputFormatter formatter) {
            _outputFormatter = formatter;
        }

        public Map<String, String[]> getParametersMap () {
            return _rawRequest.getParameterMap();
        }

        public Collection<String> getParameterNames () {
            return CollectionUtil.toList(_rawRequest.getParameterNames());
        }

        public String[] getParameterValues (String name) {
            return _rawRequest.getParameterValues(name);
        }

        public String getParameterValue (String name, String defaultValue) {
            return StringUtil.defaultString(getParameterValue(name), defaultValue);
        }

        public String getParameterValue (String name) {
            String[] values = _rawRequest.getParameterValues(name);
            return (values == null || values.length == 0) ? null : values[0];
        }

        public <T> T getParameterValueAs (String name, Class<T> as) {
            String value = getParameterValue(name);
            return Converter.convert(value, as);
        }

        public <T> T getParameterValueAs (String name, Class<T> as, T defaultValue) {
            String value = getParameterValue(name);
            T ret = (value == null) ? null : Converter.convert(value, as);
            if (ret == null) {
                ret = defaultValue;
            }
            return ret;
        }

        public void setResponseHeader(String name, String value) {
            _rawResponse.setHeader(name, value);
        }

        @Deprecated
        public HttpServletRequest getRawRequest () {
            return _rawRequest;
        }

        @Deprecated
        public HttpServletResponse getRawResponse () {
            return _rawResponse;
        }

        public int getPathCount () {
            return _path.length;
        }

        public int getRemainingPathCount () {
            int count = _path.length - _nextPathIndex;
            if (_next != null) {
                ++count;
            }
            return count;
        }

        public Status getStatus () {
            return _status;
        }
        
        public void setStatus (Status st) {
            _status = st;
        }

        public Resource getPathAt (int index) {
            return (index < _path.length) ? new Resource(_path[index]) : null;
        }

        public Iterator getPathIterator () {
            return new Iterator() {
                @Override
                public boolean hasNext () {
                    return !atEnd();
                }

                @Override
                public Object next () {
                    return popPath().getName();
                }

                @Override
                public void remove () {
                    throw new UnsupportedOperationException("Not supported");
                }
            };
        }

        public Resource peekPath () {
            if (_next == null && _nextPathIndex < _path.length) {
                _next = getPathAt(_nextPathIndex++);
            }
            return _next;
        }

        public Resource popPath () {
            _prev = peekPath();
            _next = null;
            return _prev;
        }

        public Resource prevPath () {
            return _prev;
        }

        public void writeResponse () throws IOException {
            Status status = ObjectUtil.notNull(_status, Status.OK);

            _rawResponse.setStatus(status.getCode());
            if (_content instanceof Throwable) {
                _rawResponse.setContentType("text/html");

                PrintWriter writer = _rawResponse.getWriter();
                Throwable ex = (Throwable) _content;

                writer.append("<h1>Server Error</h1>");
                
                String message = ex.getMessage();
                if (message != null && message.length() > 0) {
                    writer.append("<h2>Message</h2><b>")
                        .append(message)
                        .append("</b><br>");
                }
                writer.append("<h2>Callstack</h2><pre>");
                ex.printStackTrace(writer);
                writer.append("</pre>");
            } else {
                OutputFormatter.Wrapper wrap = _outputFormatter.wrap(_content);
                OutputFormatter.BinaryWrapper bin;
                OutputFormatter.PrintWrapper print;
                String mimetype = _outputFormatter.getMimeType();
                if(mimetype != null) {
                    _rawResponse.setContentType(_outputFormatter.getMimeType());
                }

                int size = wrap.getSize();
                if (size > 0) {
                    _rawResponse.setContentLength(size);
                }

                if (wrap instanceof OutputFormatter.BinaryWrapper) {
                    bin = (OutputFormatter.BinaryWrapper) wrap;
                    bin.writeTo(_rawResponse.getOutputStream());
                } else {
                    print = (OutputFormatter.PrintWrapper) wrap;
                    print.writeTo(_rawResponse.getWriter());
                }
            }
        }

        //---------------------------------------------------------------------

        private static final String[] _emptyPath = new String[0];

        private final String[] _path;
        private final HttpServletRequest _rawRequest;
        private final HttpServletResponse _rawResponse;

        private Action _action;
        private OutputFormatter _outputFormatter;
        private T _content;
        private int _nextPathIndex;
        private Resource _next;
        private Resource _prev;
        private Status _status;
    } // Context
}
