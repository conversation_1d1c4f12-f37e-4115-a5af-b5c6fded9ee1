/*
 * Copyright (c) 2013. Sencha Inc.
 */
package com.sencha.util.http;

import com.sencha.exceptions.BasicException;
import com.sencha.exceptions.ExInterrupted;
import com.sencha.logging.SenchaLogManager;
import java.io.IOException;

import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;

/**
 * This class dispatches requests to Responder instances.
 */
public class DispatchServlet extends HttpServlet {
    public void setRootResponder (Responder root) {
        _root = root;
    }

    @Override
    protected void service (HttpServletRequest request, HttpServletResponse response) throws IOException {
        _logger.debug("Dispatching {} {}", request.getMethod(), request.getPathInfo());
        Responder.Context context = null;
        try {
            context = new Responder.Context(request, response);
            _root.dispatch(context);
        } catch (Exception ex) {
            if (!(ex instanceof ExInterrupted) || !"/shutdown".equals(request.getRequestURI())) {
                String text = (_logger.isDebugEnabled() || _logger.isTraceEnabled())
                        ? BasicException.stringify(ex)
                        : ex.getMessage();

                if (!(ex instanceof InterruptedException) &&
                    !(ex instanceof ExInterrupted)) {
                    _logger.error("Dispatch exception {}", text);
                }

                if (context != null && context.getStatus() == null) {
                    context.setStatus(Responder.Status.INTERNAL_SERVER_ERROR);
                    context.setContent(ex);
                }
            }
        } finally {
            if(context != null) {
                context.writeResponse();
            }
        }
    }

    private static final Logger _logger = SenchaLogManager.getLogger();
    private Responder _root;
}
