/*
 * Copyright (c) 2012-2013. Sencha Inc.
 */
package com.sencha.util;

import com.sencha.exceptions.BasicException;
import com.sencha.exceptions.ExArg;
import com.sencha.util.functional.Func;

import java.io.*;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.xml.bind.DatatypeConverter;

/**
 * This class provides utility functions for strings.
 */
public final class StringUtil {
   
    public static String NewLine = System.getProperty("line.separator");

    /**
     * Converts a camelCase string into a dashed string by inserting '-' characters at the
     * transition points. The resulting string is all lowercase.
     *
     * @param camelCase The string to convert.
     * @return The converted string.
     */
    public static String dashify (String camelCase) {
        StringBuilder buffer = new StringBuilder();
        boolean nextIsLower;
        int previousUppers = 0;
        char c, next;

        // for example:  (get)FooBarHTMLStuff   --> foo-bar-html-stuff
        //              
        for (int i = 0, n = camelCase.length(); i < n; ++i) {
            c = camelCase.charAt(i);
            nextIsLower = (i + 1 < n) && Character.isLowerCase(camelCase.charAt(i + 1));

            if (i > 0) {
                if (Character.isUpperCase(c)) {
                    if (previousUppers == 0 || nextIsLower) {
                        buffer.append('-');
                    }
                    ++previousUppers;
                } else {
                    previousUppers = 0;
                }
            }

            buffer.append(c);
        }

        return buffer.toString().toLowerCase();
    }

    /**
     * Returns the first string that is not null or empty. If all strings are null or
     * empty, an empty string is returned.
     * @param strings
     * @return
     */
    public static String defaultString (String... strings) {
        String ret = notNullOrEmpty(strings);

        return (ret == null) ? "" : ret;
    }

    public static final String formatString (String format, Object... args) {
        return String.format(format, args);
    }

    public static String formatTemplate (String format, Object... args) {
        return MessageFormat.format(format, args);
    }

    public static int getMatchLength (String str1, String str2) {
        int n = Math.min(str1.length(), str2.length());

        for (int i = 0; i < n; ++i) {
            if (str1.charAt(i) != str2.charAt(i)) {
                return i;
            }
        }

        return n;
    }

    /**
     * returns a ByteArrayInputStream for the string
     * @param data
     * @return
     */
    public static InputStream getStream (String data) {
        return new ByteArrayInputStream(data.getBytes());
    }

    /**
     * combines all input parts with the specified separator
     * @param parts
     * @param separator
     * @return
     */
    public static String join (Object[] parts, String separator) {
        if (parts == null || parts.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();

        sb.append(parts[0]);

        for (int i = 1; i < parts.length; i++) {
            sb.append(separator);
            sb.append(parts[i]);
        }

        return sb.toString();
    }

    
    /**
     * combines all input parts as a string separated by a comma (,)
     * @param parts
     * @return 
     */
    public static String join(Object[] parts) {
        return join(parts, ",");
    }
    
    public static String join (Collection parts, String separator){
        return join(parts.toArray(new Object[parts.size()]), separator);
    }

    /**
     * combines all input parts as a string separated by a comma (,)
     * @param parts
     * @return 
     */
    public static String join(Collection parts) {
        return join(parts, ",");
    }
    
    /**
     * Escapes a string for usage with message format.  Wraps any existing '{}' chars in
     * single quotes, eg { becomes '{'
     * @param str
     * @return
     */
    public static String escapeForLogging (String str) {
//        if(SenchaLogManager.HandlerClass.equals(SenchaConsoleHandler.class)) {
//            return str;
//        }
        Pattern pat = RegexUtil.getInstance().get("(\\{|\\}|\\')");
        Matcher m = pat.matcher(str);
        return m.replaceAll("'$1'");
    }

    /**
     * Returns the first string that is not null or empty. If all strings are null or
     * empty, null is returned.
     * @param strings
     * @return
     */
    public static String notNullOrEmpty (String... strings) {
        for (String s: strings) {
            if (!isNullOrEmpty(s)) {
                return s;
            }
        }
        return null;
    }

    /**
     * tests a string to see if null or empty
     * @param s
     * @return
     */
    public static boolean isNullOrEmpty (String s) {
        return s == null || s.isEmpty();
    }

    public static int countNullOrEmpty (String... strings) {
        int count = 0;
        for (String s : strings) {
            if (isNullOrEmpty(s)) {
                ++count;
            }
        }
        return count;
    }

    public static byte[] fromBase64 (String text) {
        return DatatypeConverter.parseBase64Binary(text);
    }

    public static String toBase64 (byte[] bytes) {
        return DatatypeConverter.printBase64Binary(bytes);
    }

    public static String repeat (int count, char ch) {
        StringBuilder buf = new StringBuilder();
        for (int i = 0; i < count; ++i) {
            buf.append(ch);
        }
        return buf.toString();
    }

    public static String repeat (int count) {
        return repeat(count, ' ');
    }
    
    /**
     * splits a string with the given separator
     * will return an empty array for a null or empty input string
     * @param input the input string to split
     * @param separator the separator to split on
     * @return the array of strings (possibly empty)
     */
    public static String[] split(String input, String separator) {
        if(isNullOrEmpty(input)) {
            return new String[]{};
        }
        List<Pair<Integer, Integer>> parts = new ArrayList<Pair<Integer, Integer>>();
        int start = 0;
        int end = start;
        while(end < input.length()) {
            end = input.indexOf(separator, start);
            if(end == -1) {
                if(start < input.length()) {
                    parts.add(new Pair<Integer, Integer>(start, -1));
                }
                break;
            }
            parts.add(new Pair<Integer, Integer>(start, end));
            start = end + separator.length();
            end = start;
        } 
        String[] substrings = new String[parts.size()];
        for(int x = 0; x < substrings.length; x++) {
            Pair<Integer, Integer> pair = parts.get(x);
            start = pair.getLeft();
            end = pair.getRight();
            if(end == -1) {
                substrings[x] = input.substring(start);
            } else {
                substrings[x] = input.substring(start, end);
            }
        }
        return substrings;
    }
    
    /**
     * splits a string on a command (',') character
     * @param input
     * @return 
     */
    public static String[] split(String input) {
        return split(input, ",");
    }
    
    /**
     * replaces all occurrences of the match string in the input string with
     * the replacement string.
     * @param input
     * @param match
     * @param replacement
     * @return
     */
    public static String replace(String input, String match, String replacement) {
        if(isNullOrEmpty(input)) {
            return input;
        }
        return input.replace(match, replacement);
    }


    public static String replaceAll(String input, String pat, String replacement) {
        Pattern p = RegexUtil.getInstance().get(pat);
        Matcher m = p.matcher(input);
        return m.replaceAll(replacement);
    }

    public static String replaceAll (String input, Pattern regex, Func<String, String> fn) {
        StringBuffer ret = new StringBuffer();
        Matcher match;
        String s;

        for (match = regex.matcher(input); match.find(); ) {
            s = fn.map(match.group(1));
            s = Matcher.quoteReplacement(s);
            match.appendReplacement(ret, s);
        }
        
        match.appendTail(ret);
        return ret.toString();
    }

    public static String replaceFirst(String input, String pat, String replacement) {
        Pattern p = RegexUtil.getInstance().get(pat);
        Matcher m = p.matcher(input);
        return m.replaceFirst(replacement);
    }

    public static boolean matches(String input, String pat) {
        Pattern p = RegexUtil.getInstance().get(pat);
        Matcher m = p.matcher(input);
        return m.matches();
    }

    public static int getCommonPrefixLength (String str1, String str2) {
        int len = Math.min(str1.length(), str2.length());
        
        for (int i = 0; i < len; ++i) {
            if (str1.charAt(i) != str2.charAt(i)) {
                return i;
            }
        }

        return len;
    }

    public static String normalizeLineEndings (String input) {
        return input.replace("\r\r\n", "\n").replace("\r\n", "\n");
    }

    public static String normalizeContent(String content) {

    content = normalizeLineEndings(content);

    // Remove extra blank lines
    content = content.replaceAll("(?m)^\\s*$\\n?", "");

    // Normalize indentation by trimming extra spaces at the start of each line
    content = content.replaceAll("(?m)^\\s+", "    ");

    return content;
    }

    public static String stripSingleLineComments(String input) {
        if(isNullOrEmpty(input)) {
            return input;
        }
        
        Pattern p = RegexUtil.getInstance().get("(^|\\s+?)//.*?$", Pattern.MULTILINE);
        Matcher m = p.matcher(input);
        boolean match = m.find();
        if(match) {
            return m.replaceAll("");
        } else {
            return input;
        }
    }
    
    public static String stripCommentBlocks(String input) {
        if(isNullOrEmpty(input)) {
            return input;
        }
        
        Pattern p = RegexUtil.getInstance().get("/\\*.*?\\*/", Pattern.DOTALL);
        Matcher m = p.matcher(input);
        boolean match = m.find();
        if(match) {
            return m.replaceAll("");
        } else {
            return input;
        }
    }
    
    public static String stripCommentsFromJson(String json){
        String output = json;
        output = stripSingleLineComments(output);
        output = stripCommentBlocks(output);
        return output;
    }

    public static String convertToHex(byte[] data) {
        StringBuilder buf = new StringBuilder();
        for (int i = 0; i < data.length; i++) {
            int halfbyte = (data[i] >>> 4) & 15;
            int two_halfs = 0;
            do {
                if ((0 <= halfbyte) && (halfbyte <= 9)) {
                    buf.append((char) ('0' + halfbyte));
                } else {
                    buf.append((char) ('a' + (halfbyte - 10)));
                }
                halfbyte = data[i] & 15;
            } while (two_halfs++ < 1);
        }
        return buf.toString();
    }

    public static String fromBytes (byte[] bytes, String charset) {
        String cs = (charset == null) ? "UTF-8" : charset;

        try {
            return new String(bytes, cs);
        }
        catch (UnsupportedEncodingException ex) {
            throw new ExArg(ex, "Charset {0} is not supported", cs);
        }
    }

    public static String fromBytes (byte[] bytes) {
        return fromBytes(bytes, null);
    }

    public static byte[] getBytes (String str, String charset) {
        String cs = (charset == null) ? "UTF-8" : charset;

        try {
            return str.getBytes(cs);
        } catch (UnsupportedEncodingException ex) {
            throw new ExArg(ex, "Charset {0} is not supported", cs);
        }
    }

    public static byte[] getBytes (String content) {
        return getBytes(content, null);
    }

    public static Map<String, Object> getOptionsFromString(String opts) {
        Map<String, Object> options = new HashMap<String, Object>();
        if (StringUtil.isNullOrEmpty(opts)) {
            return options;
        }
        for (String kvp : opts.split(",")) {
            if (!StringUtil.isNullOrEmpty(kvp)) {
                int sep = kvp.indexOf(":");
                if(sep > -1) {
                    String name = kvp.substring(0, sep);
                    String value = kvp.substring(sep + 1, kvp.length());
                    options.put(name, value);
                }
            }
        }
        return options;
    }

    public static boolean isFalse (String s) {
        return s != null && _falseRe.matcher(s).matches();
    }

    public static boolean isTrue (String s) {
        return s != null && _trueRe.matcher(s).matches();
    }

    public static String createChecksum (String content) {
        return createChecksum(content, CharsetDetector.DefaultCharset);
    }

    public static String createChecksum (String content, Charset charset) {
        try {
            return createChecksum(content.getBytes(charset));
        } catch (Exception ex) {
            throw new BasicException(ex, "failed calculating SHA-1 hash").raise();
        }
    }

    public static String createChecksum (byte[] data) {
        return createChecksum(data, "SHA-1");
    }

    public static String createChecksum (byte[] data, String hashType) {
        try {
            MessageDigest md;
            md = MessageDigest.getInstance(hashType);
            md.update(data, 0, data.length);
            byte[] hash = md.digest();
            return convertToHex(hash);
        } catch (Exception ex) {
            throw new BasicException(ex, "failed calculating {0} hash", hashType).raise();
        }
    }

    public static String removeDuplicateLineEndings(String input) {
        input = normalizeLineEndings(input);
        StringReader reader = new StringReader(input);
        BufferedReader buff = new BufferedReader(reader);
        List<String> lines = new ArrayList<String>();
        String line;
        try {
            boolean blank = false;
            Pattern p = Pattern.compile("^\\s*$");
            while ((line = buff.readLine()) != null) {
                Matcher m = p.matcher(line);
                if (m.find()) {
                    blank = true;
                }
                else {
                    if (blank) {
                        blank = false;
                        lines.add("");
                    }
                    lines.add(line);
                }
            }
            if (blank) {
                lines.add("");
            }
            // if the input content ended in a new line, need to add a new one
            if (input.endsWith("\n")) {
                lines.add("");
            }
            String result = join(lines, NewLine);
            return result;
        }
        catch (Exception ex) {
            throw BasicException.raise(ex);
        }
    }

    public static class Slice implements CharSequence {
        public Slice (CharSequence str, int offset, int len) {
            _string = str;
            int strlen = str.length();
            _offset = boundIndex(offset, strlen);
            _length = boundLength(len, _offset, strlen);
        }

        public Slice (CharSequence str) {
            this(str, 0, str.length());
        }

        public CharSequence get () {
            return _string;
        }

        public int getOffset () {
            return _offset;
        }
        public void setOffset (int offset) {
            _offset = offset;
        }

        public void setLength (int len) {
            _length = len;
        }

        @Override
        public int length () {
            return _length;
        }

        @Override
        public char charAt (int index) {
            return _string.charAt(_offset + index);
        }

        @Override
        public CharSequence subSequence (int start, int end) {
            int begin = boundIndex(start, _length);
            int len = boundLength(end - start, begin, _length);

            return new Slice(_string, _offset + begin, len);
        }

        private static int boundIndex (int index, int length) {
            if (index < 0) {
                return 0;
            }
            if (index < length) {
                return index;
            }
            return (length == 0) ? 0 : (length - 1);
        }

        private static int boundLength (int n, int start, int length) {
            if (n < 0) {
                return 0;
            }
            int rem = length - start;
            if (start + n < rem) {
                return n;
            }
            return rem;
        }

        private final CharSequence _string;
        private int _offset;
        private int _length;
    }

    public static Slice slice (CharSequence chars, int offset, int length) {
        return new Slice(chars, offset, length);
    }

    public static Slice slice (CharSequence chars) {
        return new Slice(chars);
    }

    public static <T> T parseEnum (String val, Class<T> targetType) {
        if (val == null) {
            return null;
        }

        Object[] enums = targetType.getEnumConstants();
        Object match = null;
        int matches = 0;

        for (Object en : enums) {
            String s = en.toString();
            if (s.equals(val)) {
                return (T) en;
            }

            if (s.equalsIgnoreCase(val)) {
                match = en;
                ++matches;
            }
        }

        if (matches == 1) {
            return (T) match;
        }

        return null;
    }

    public static String capitalize (String name) {
        return name.substring(0, 1).toUpperCase() + name.substring(1);
    }

    public static class Escaper {
        public Escaper (String... esc) {
            for (int i = 0; i < esc.length; ++i) {
                String s = esc[i];
                _escapes[Character.codePointAt(s, 0)] = s.substring(1);
            }
        }

        public String escape (String str) {
            StringBuilder out = new StringBuilder();
            int count = _escapes.length;
            int cp;
            String esc;

            for (int i = 0, n = (str == null) ? 0 : str.length(); i < n; ++i) {
                cp = Character.codePointAt(str, i);
                esc = (cp < count) ? _escapes[cp] : null;

                if (esc == null) {
                    out.appendCodePoint(cp);
                } else {
                    out.append(esc);
                }
            }

            return out.toString();
        }

        private String[] _escapes = new String[127];
    }

    // TODO - replace w/real XML encoder
    public static String escapeXml (String s) {
        return _baseXmlEscaper.escape(s);
    }

    public static String escapeXmlAttr (String s) {
        return _attrXmlEscaper.escape(s);
    }

    public static InputStream getInputStream(String str) {
        return new ByteArrayInputStream(getBytes(str));
    }
    
    public static List<String> split(String str, int width) {
        List<String> parts = new ArrayList<String>();
        for(int x = 0; x < str.length() - width; x+=width) {
            parts.add(str.substring(x, x+width));
        }
        return parts;
    }

    public static String removeLineEndings(String input) {
        StringBuilder buff = new StringBuilder();
        for(char c : input.toCharArray()) {
            if((c != '\n') && (c != '\r')) {
                buff.append(c);
            }
        }
        return buff.toString();
    }
    
    
    public static String pluralize (int value, String noun) {
        return pluralize(value, noun, noun.concat("s"));
    }
    
    public static String pluralize (int value, String noun, String nounIfPlural) {
        return (value == 1) ? noun : nounIfPlural;
    }
    
    //-------------------------------------------------------------------------

    private static final Escaper
            _baseXmlEscaper = new Escaper("<&lt;", "&&amp;", ">&gt;");

    private static final Escaper
            _attrXmlEscaper = new Escaper("<&lt;", "&&amp;", ">&gt;", "\"&quot;");

    private static final Pattern
            _falseRe = Pattern.compile("^(0|off|false|no)$", Pattern.CASE_INSENSITIVE);

    private static final Pattern
            _trueRe = Pattern.compile("^(1|on|true|yes)$", Pattern.CASE_INSENSITIVE);
}
