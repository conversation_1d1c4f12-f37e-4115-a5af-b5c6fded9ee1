{"name": "@sencha/ext-font-awesome", "ext-react-name": "@sencha/ext-react-font-awesome", "ext-name": "@sencha/ext-font-awesome", "SenchaExtName": "@sencha/ext-font-awesome", "SenchaExtReactName": "@sencha/ext-react-font-awesome", "version": "********", "sencha": {"name": "font-awesome", "namespace": "Ext", "type": "code", "framework": "ext", "toolkit": "classic", "theme": "theme-neptune", "creator": "<PERSON><PERSON>", "summary": "Package wrapper for Font Awesome", "detailedDescription": "Sencha Cmd package wrapper for Font Awesome (http://fortawesome.github.io/Font-Awesome/)", "version": "********", "compatVersion": "5.6.3", "format": "1", "output": "${framework.dir}/build/packages/${package.name}", "local": true, "resource": {"paths": ""}, "resources": [{"path": "${package.dir}/resources", "output": "shared"}], "sass": {"namespace": "Ext", "etc": "${package.dir}/sass/etc/all.scss", "var": "${package.dir}/sass/var", "src": ["${package.dir}/sass/src", "${package.dir}/sass/src/all.scss"]}, "classpath": "${package.dir}/src", "overrides": "${package.dir}/overrides", "slicer": null}}