<?xml version="1.0" encoding="UTF-8"?>
<install4j version="10.0.6" transformSequenceNumber="10">
  <directoryPresets config="./JRE" />
  <application name="Sencha Cmd" applicationId="3406-4789-1226-3866" mediaDir="./.build" mediaFilePattern="${compiler:sys.shortName}-${compiler:sys.version}-${compiler:sys.platform}" compression="8" lzmaCompression="true" shortName="SenchaCmd" publisher="Sencha Inc." publisherWeb="http://sencha.com" version="7.7.0" allPathsRelative="true" backupOnSave="true" autoSave="true" convertDotsToUnderscores="false" macVolumeId="18b62f6a529860b0" javaMinVersion="17" javaMaxVersion="99" jdkMode="jdk" jdkName="JDK 17">
    <searchSequence>
      <envVar name="JAVA_HOME" />
      <envVar name="JDK_HOME" />
      <envVar name="PATH" />
      <registry />
    </searchSequence>
    <variables>
      <variable name="cmd.dir.suffix" value="Sencha/Cmd/${compiler:sys.version}" description="The sub folder appended to the selected installation directory" />
    </variables>
    <codeSigning macEnabled="true" macPkcs12File="~/Desktop/sencha_idera_signing.p12" macNotarize="true" appleId="<EMAIL>" useAscProvider="true" ascProvider="9A24T5X23Y">
      <macAdditionalBinaries>
        <entry>*.dylib</entry>
        <entry>*.so</entry>
        <entry>*.jnilib</entry>
        <entry>*.jar</entry>
        <entry>*.dat</entry>
        <entry>26.dat</entry>
        <entry>28.dat</entry>
        <entry>Update.dylib</entry>
        <entry>node</entry>
        <entry>vcdiff</entry>
        <entry>phantomjs</entry>
        <entry>*.txt</entry>
        <entry>*.zip</entry>
        <entry>*.bin</entry>
        <entry>update-1.1.0.jar</entry>
        <entry>update</entry>
        <entry>Update.*</entry>
      </macAdditionalBinaries>
      <macSearchedJars>
        <entry>jna-*</entry>
        <entry>swt-*</entry>
        <entry>update-*</entry>
        <entry>update-1.1.0.jar</entry>
      </macSearchedJars>
    </codeSigning>
    <jreBundles jdkProviderId="AdoptOpenJDK" release="17/jdk-17.0.12+7" />
  </application>
  <files>
    <filesets>
      <fileset name="Windows" id="318" />
      <fileset name="Mac OS" id="320" />
      <fileset name="Linux 32 bit" id="322" />
      <fileset name="Linux 64 bit" id="324" />
    </filesets>
    <roots>
      <root id="710" location="${installer:sys.installationDir}/.." />
      <root id="319" fileset="318" />
      <root id="709" fileset="318" location="${installer:sys.installationDir}/.." />
      <root id="321" fileset="320" />
      <root id="323" fileset="322" />
      <root id="325" fileset="324" />
    </roots>
    <mountPoints>
      <mountPoint id="64" />
      <mountPoint id="711" root="710" />
      <mountPoint id="328" root="319" />
      <mountPoint id="712" root="709" />
      <mountPoint id="329" root="321" />
      <mountPoint id="330" root="323" />
      <mountPoint id="331" root="325" />
    </mountPoints>
    <entries>
      <dirEntry mountPoint="64" file="./.build/dist/all" subDirectory="Cmd" />
      <fileEntry mountPoint="711" file="./.build/dist/switch/sencha" overwriteMode="1" fileMode="755" overrideFileMode="true" overrideOverwriteMode="true" overrideUninstallMode="true" />
      <fileEntry mountPoint="711" file="./.build/dist/switch/version.properties" overwriteMode="1" fileMode="755" overrideFileMode="true" overrideOverwriteMode="true" overrideUninstallMode="true" />
      <dirEntry mountPoint="328" file="./.build/dist/win/bin" fileMode="755" overrideFileMode="true" entryMode="subdir" subDirectory="bin" overrideDirMode="true" />
      <fileEntry mountPoint="712" file="./.build/dist/switch/sencha.exe" overwriteMode="1" fileMode="755" overrideFileMode="true" overrideOverwriteMode="true" overrideUninstallMode="true" />
      <dirEntry mountPoint="329" file="./.build/dist/mac/bin" fileMode="755" overrideFileMode="true" entryMode="subdir" subDirectory="bin" overrideDirMode="true" />
      <dirEntry mountPoint="330" file="./.build/dist/linux-x86/bin" fileMode="755" overrideFileMode="true" entryMode="subdir" subDirectory="bin" overrideDirMode="true" />
      <dirEntry mountPoint="331" file="./.build/dist/linux-x64/bin" fileMode="755" overrideFileMode="true" entryMode="subdir" subDirectory="bin" overrideDirMode="true" />
    </entries>
    <components>
      <component name="Common files" id="26" changeable="false" hidden="true">
        <include>
          <entry location="ant" />
          <entry location="js" />
          <entry location="lib" />
          <entry location="logo-sencha-sm.png" />
          <entry location="plugin.xml" />
          <entry location="plugins" />
          <entry location="release-notes.html" />
          <entry location="rhino-require.js" />
          <entry location="resolver.js" />
          <entry location="sass-page-loader.js" />
          <entry location="shell-wrapper.sh" />
          <entry location="screenshot-runner.js" />
          <entry location="sencha.cfg" />
          <entry location="sencha.jar" />
          <entry location="templates" />
          <entry location="unicode-escapes.json" />
          <entry location="sencha" fileType="launcher" />
          <entry rootId="710" />
        </include>
        <dependencies>
          <component id="595" />
        </dependencies>
      </component>
      <component name="windows" id="27" changeable="false" hidden="true">
        <include>
          <entry filesetId="318" />
        </include>
        <dependencies>
          <component id="26" />
        </dependencies>
      </component>
      <component name="mac" id="28" changeable="false" hidden="true">
        <include>
          <entry filesetId="320" />
        </include>
        <dependencies>
          <component id="26" />
        </dependencies>
      </component>
      <component name="linux-x86" id="29" changeable="false" hidden="true">
        <include>
          <entry filesetId="322" />
        </include>
        <dependencies>
          <component id="26" />
        </dependencies>
      </component>
      <component name="linux-x64" id="30" changeable="false" hidden="true">
        <include>
          <entry filesetId="324" />
        </include>
        <dependencies>
          <component id="26" />
        </dependencies>
      </component>
      <component name="legacy" id="90" changeable="false" hidden="true">
        <include>
          <entry location="legacy" />
        </include>
        <dependencies>
          <component id="26" />
        </dependencies>
      </component>
      <component name="Compass extension" id="91" selected="false">
        <description>This package is required to build styling for Ext JS 4.x, 5.x, and Sencha Touch 2.x.</description>
        <include>
          <entry location="extensions/sencha-compass" />
        </include>
        <dependencies>
          <component id="26" />
        </dependencies>
      </component>
      <component name="Service extension" id="92" changeable="false">
        <include>
          <entry location="extensions/sencha-service" />
        </include>
        <dependencies>
          <component id="26" />
        </dependencies>
      </component>
      <component name="Fashion extension" id="148" changeable="false">
        <include>
          <entry location="extensions/sencha-fashion" />
        </include>
        <dependencies>
          <component id="26" />
        </dependencies>
      </component>
      <component name="Packager extension" id="94" changeable="false">
        <include>
          <entry location="extensions/cmd-packager" />
        </include>
        <dependencies>
          <component id="26" />
        </dependencies>
      </component>
      <component name="license-extension" id="157" changeable="false" hidden="true">
        <include>
          <entry location="extensions/sencha-licenses" />
        </include>
        <dependencies>
          <component id="26" />
        </dependencies>
      </component>
    </components>
  </files>
  <launchers>
    <launcher name="sencha" id="159">
      <executable name="sencha" iconSet="true" executableDir="." redirectStderr="false" executableMode="console" changeWorkingDirectory="false" dpiAware="false" />
      <java mainClass="com.sencha.command.Sencha" vmParameters="-Dapple.awt.UIElement=true" preferredVM="server">
        <classPath>
          <archive location="sencha.jar" failOnError="false" />
        </classPath>
      </java>
      <vmOptionsFile mode="content">
        <content># Enter one VM parameter per line
# For example, to adjust the maximum memory usage to 512 MB, use the following line:
# -Xmx512m
# To include another file, uncomment the following line:
# -include-options [path to other .vmoption file]

-Xms256m
-Xmx2048m
-Dapple.awt.UIElement=true</content>
      </vmOptionsFile>
      <iconImageFiles>
        <file path="./installers/sencha-tools-16.png" />
        <file path="./installers/sencha-tools-32.png" />
        <file path="./installers/sencha-tools-48.png" />
        <file path="./installers/sencha-tools-128.png" />
      </iconImageFiles>
    </launcher>
  </launchers>
  <installerGui suggestPreviousLocations="false">
    <applications>
      <application id="installer" beanClass="com.install4j.runtime.beans.applications.InstallerApplication" styleId="1109" customIcnsFile="./installers/sencha-tools.icns" customIcoFile="./installers/sencha-tools.ico" launchInNewProcess="false">
        <serializedBean>
          <property name="customIconImageFiles">
            <add>
              <object class="com.install4j.api.beans.ExternalFile">
                <string>./installers/sencha-tools-128.png</string>
              </object>
            </add>
          </property>
          <property name="useCustomIcon" type="boolean" value="true" />
        </serializedBean>
        <styleOverrides>
          <styleOverride name="Customize title bar" enabled="true">
            <group id="1123" beanClass="com.install4j.runtime.beans.groups.VerticalFormComponentGroup">
              <serializedBean>
                <property name="backgroundColor">
                  <object class="com.install4j.runtime.beans.LightOrDarkColor">
                    <object class="java.awt.Color">
                      <int>255</int>
                      <int>255</int>
                      <int>255</int>
                      <int>255</int>
                    </object>
                    <object class="java.awt.Color">
                      <int>49</int>
                      <int>52</int>
                      <int>53</int>
                      <int>255</int>
                    </object>
                  </object>
                </property>
                <property name="imageAnchor" type="enum" class="com.install4j.api.beans.Anchor" value="NORTHEAST" />
                <property name="imageFile">
                  <object class="com.install4j.api.beans.ExternalFile">
                    <string>./installers/sencha-tools-32.png</string>
                  </object>
                </property>
              </serializedBean>
            </group>
          </styleOverride>
          <styleOverride name="Custom watermark" enabled="true">
            <formComponent name="Watermark" id="1113" beanClass="com.install4j.runtime.beans.formcomponents.SeparatorComponent">
              <serializedBean>
                <property name="labelText" type="string">Sencha Cmd ${compiler:sys.version} Installer</property>
              </serializedBean>
            </formComponent>
          </styleOverride>
        </styleOverrides>
        <startup>
          <screen id="1" beanClass="com.install4j.runtime.beans.screens.StartupScreen" rollbackBarrierExitCode="0">
            <actions>
              <action id="13" beanClass="com.install4j.runtime.beans.actions.misc.RequestPrivilegesAction" actionElevationType="none">
                <serializedBean>
                  <property name="obtainIfAdminWin" type="boolean" value="false" />
                </serializedBean>
              </action>
              <action id="794" beanClass="com.install4j.runtime.beans.actions.control.RunScriptAction">
                <serializedBean>
                  <property name="script">
                    <object class="com.install4j.api.beans.ScriptProperty">
                      <property name="value" type="string">for (InstallationComponentSetup component : context.getInstallationComponents()) {
    component.setSelected(true); 
} 
return true; </property>
                    </object>
                  </property>
                </serializedBean>
                <condition>String allComps = System.getProperty("all");
return allComps != null &amp;&amp; !(allComps.toLowerCase().startsWith("f") || allComps.toLowerCase().startsWith("n") || allComps.equals("0"));</condition>
              </action>
            </actions>
          </screen>
        </startup>
        <screens>
          <screen id="2" beanClass="com.install4j.runtime.beans.screens.WelcomeScreen" styleId="1115">
            <styleOverrides>
              <styleOverride name="Customize banner image" enabled="true">
                <group id="1116" beanClass="com.install4j.runtime.beans.groups.VerticalFormComponentGroup">
                  <serializedBean>
                    <property name="imageEdgeBackgroundColor">
                      <object class="java.awt.Color">
                        <int>255</int>
                        <int>255</int>
                        <int>255</int>
                        <int>255</int>
                      </object>
                    </property>
                    <property name="imageFile">
                      <object class="com.install4j.api.beans.ExternalFile">
                        <string>./installers/sencha-tools-128.png</string>
                      </object>
                    </property>
                  </serializedBean>
                </group>
              </styleOverride>
            </styleOverrides>
            <actions>
              <action id="3" beanClass="com.install4j.runtime.beans.actions.misc.LoadResponseFileAction" multiExec="true">
                <serializedBean>
                  <property name="excludedVariables" type="array" elementType="string" length="1">
                    <element index="0">sys.installationDir</element>
                  </property>
                </serializedBean>
                <condition>context.getBooleanVariable("sys.confirmedUpdateInstallation")</condition>
              </action>
            </actions>
            <formComponents>
              <formComponent id="799" beanClass="com.install4j.runtime.beans.formcomponents.MultilineLabelComponent">
                <serializedBean>
                  <property name="labelText" type="string">${form:welcomeMessage}</property>
                </serializedBean>
                <visibilityScript>!context.isConsole()</visibilityScript>
              </formComponent>
              <formComponent id="800" beanClass="com.install4j.runtime.beans.formcomponents.ConsoleHandlerFormComponent">
                <serializedBean>
                  <property name="consoleScript">
                    <object class="com.install4j.api.beans.ScriptProperty">
                      <property name="value" type="string">String message = context.getMessage("ConsoleWelcomeLabel", context.getApplicationName());
return console.askOkCancel(message, true);
</property>
                    </object>
                  </property>
                </serializedBean>
              </formComponent>
              <formComponent id="801" beanClass="com.install4j.runtime.beans.formcomponents.UpdateAlertComponent" useExternalParametrization="true" externalParametrizationName="Update Alert" externalParametrizationMode="include">
                <serializedBean>
                  <property name="updateCheck" type="boolean" value="false" />
                </serializedBean>
                <externalParametrizationPropertyNames>
                  <propertyName>updateCheck</propertyName>
                </externalParametrizationPropertyNames>
              </formComponent>
              <formComponent id="802" beanClass="com.install4j.runtime.beans.formcomponents.MultilineLabelComponent" insetTop="20">
                <serializedBean>
                  <property name="labelText" type="string">${i18n:ClickNext}</property>
                </serializedBean>
              </formComponent>
            </formComponents>
          </screen>
          <screen id="473" beanClass="com.install4j.runtime.beans.screens.LicenseScreen">
            <formComponents>
              <formComponent id="805" beanClass="com.install4j.runtime.beans.formcomponents.MultilineLabelComponent">
                <serializedBean>
                  <property name="labelText" type="string">${i18n:LicenseLabel3}</property>
                </serializedBean>
              </formComponent>
              <formComponent id="806" beanClass="com.install4j.runtime.beans.formcomponents.LicenseComponent" useExternalParametrization="true" externalParametrizationName="License Agreement" externalParametrizationMode="include">
                <serializedBean>
                  <property name="displayedTextFile" id="LocalizedExternalFile0">
                    <property name="languageIdToExternalFile">
                      <entry>
                        <string>en</string>
                        <object class="com.install4j.api.beans.ExternalFile">
                          <string>./installers/LICENSE</string>
                        </object>
                      </entry>
                    </property>
                  </property>
                  <property name="fillVertical" type="boolean" value="true" />
                </serializedBean>
                <externalParametrizationPropertyNames>
                  <propertyName>textSource</propertyName>
                  <propertyName>displayedText</propertyName>
                  <propertyName>displayedTextFile</propertyName>
                  <propertyName>variableName</propertyName>
                  <propertyName>acceptInitiallySelected</propertyName>
                  <propertyName>readAllRequired</propertyName>
                </externalParametrizationPropertyNames>
              </formComponent>
            </formComponents>
          </screen>
          <screen id="4" beanClass="com.install4j.runtime.beans.screens.InstallationDirectoryScreen">
            <condition>!context.getBooleanVariable("sys.confirmedUpdateInstallation")</condition>
            <actions>
              <action id="5" beanClass="com.install4j.runtime.beans.actions.misc.LoadResponseFileAction" multiExec="true">
                <serializedBean>
                  <property name="excludedVariables" type="array" elementType="string" length="1">
                    <element index="0">sys.installationDir</element>
                  </property>
                </serializedBean>
                <condition>context.getVariable("sys.responseFile") == null</condition>
              </action>
            </actions>
            <formComponents>
              <formComponent id="810" beanClass="com.install4j.runtime.beans.formcomponents.MultilineLabelComponent" insetBottom="25">
                <serializedBean>
                  <property name="labelText" type="string">${i18n:SelectDirLabel(${compiler:sys.fullName})}</property>
                </serializedBean>
              </formComponent>
              <formComponent id="811" beanClass="com.install4j.runtime.beans.formcomponents.InstallationDirectoryChooserComponent" useExternalParametrization="true" externalParametrizationName="Installation Directory Chooser" externalParametrizationMode="include">
                <serializedBean>
                  <property name="existingDirWarning" type="boolean" value="false" />
                  <property name="requestFocus" type="boolean" value="true" />
                  <property name="suggestAppDir" type="boolean" value="false" />
                  <property name="validateApplicationId" type="boolean" value="false" />
                </serializedBean>
                <externalParametrizationPropertyNames>
                  <propertyName>suggestAppDir</propertyName>
                  <propertyName>validateApplicationId</propertyName>
                  <propertyName>existingDirWarning</propertyName>
                  <propertyName>checkWritable</propertyName>
                  <propertyName>manualEntryAllowed</propertyName>
                  <propertyName>checkFreeSpace</propertyName>
                  <propertyName>showRequiredDiskSpace</propertyName>
                  <propertyName>showFreeDiskSpace</propertyName>
                  <propertyName>allowSpacesOnUnix</propertyName>
                  <propertyName>validationScript</propertyName>
                  <propertyName>standardValidation</propertyName>
                </externalParametrizationPropertyNames>
              </formComponent>
            </formComponents>
          </screen>
          <screen id="6" beanClass="com.install4j.runtime.beans.screens.ComponentsScreen">
            <actions>
              <action id="959" beanClass="com.install4j.runtime.beans.actions.control.SetVariableAction">
                <serializedBean>
                  <property name="script">
                    <object class="com.install4j.api.beans.ScriptProperty">
                      <property name="value" type="string">java.util.ArrayList&lt;String&gt; files = new ArrayList&lt;String&gt;(java.util.Arrays.asList(".zshrc",".bashrc",".bash_profile"));
String homeDir = (String)context.getVariable("sys.userHome");

// Check if any of the possible files exist
for (String fileName : files) {
    if (new File(homeDir, fileName).exists()){
        return fileName;
    }
}
// Fallback
return ".profile";</property>
                    </object>
                  </property>
                  <property name="variableName" type="string">profile_file</property>
                </serializedBean>
              </action>
            </actions>
            <formComponents>
              <formComponent id="961" beanClass="com.install4j.runtime.beans.formcomponents.MultilineLabelComponent">
                <serializedBean>
                  <property name="labelText" type="string">${i18n:SelectComponentsLabel2}</property>
                </serializedBean>
                <visibilityScript>!context.isConsole()</visibilityScript>
              </formComponent>
              <formComponent id="962" beanClass="com.install4j.runtime.beans.formcomponents.ComponentSelectorComponent" useExternalParametrization="true" externalParametrizationName="Installation Components" externalParametrizationMode="include">
                <serializedBean>
                  <property name="fillVertical" type="boolean" value="true" />
                </serializedBean>
                <externalParametrizationPropertyNames>
                  <propertyName>selectionChangedScript</propertyName>
                </externalParametrizationPropertyNames>
              </formComponent>
            </formComponents>
          </screen>
          <screen name="Ask if should add to PATH" id="898" beanClass="com.install4j.runtime.beans.screens.AdditionalConfirmationsScreen">
            <condition>context.getVariable("addToPath") == null</condition>
            <formComponents>
              <formComponent id="1015" beanClass="com.install4j.runtime.beans.formcomponents.MultilineLabelComponent" insetBottom="10">
                <serializedBean>
                  <property name="labelText" type="string">${form:confirmationMessage}</property>
                </serializedBean>
                <visibilityScript>!context.isConsole()</visibilityScript>
              </formComponent>
              <formComponent id="903" beanClass="com.install4j.runtime.beans.formcomponents.MultilineHtmlLabelComponent">
                <serializedBean>
                  <property name="labelHtml" type="string">Do you want to add &lt;strong&gt;&lt;em&gt;sencha&lt;/em&gt;&lt;/strong&gt; and &lt;strong&gt;&lt;em&gt;sencha-${compiler:sys.version}&lt;/em&gt;&lt;/strong&gt; to your PATH?&lt;br /&gt;
</property>
                </serializedBean>
              </formComponent>
              <formComponent id="957" beanClass="com.install4j.runtime.beans.formcomponents.RadiobuttonsComponent">
                <serializedBean>
                  <property name="helpText" type="string">If the sencha binary is already present in your PATH, this won't affect it.</property>
                  <property name="radioButtonLabels" type="array" elementType="string" length="2">
                    <element index="0">Yes</element>
                    <element index="1">No</element>
                  </property>
                  <property name="variableName" type="string">addToPath</property>
                </serializedBean>
              </formComponent>
              <formComponent id="1013" beanClass="com.install4j.runtime.beans.formcomponents.SpringComponent" resetInitOnPrevious="true">
                <serializedBean>
                  <property name="axisType" type="enum" class="com.install4j.runtime.beans.formcomponents.AxisType" value="VERTICAL" />
                </serializedBean>
              </formComponent>
              <formComponent id="1012" beanClass="com.install4j.runtime.beans.formcomponents.MultilineHtmlLabelComponent">
                <serializedBean>
                  <property name="labelHtml" type="string">An entry will be added to your &lt;strong&gt;${installer:profile_file}&lt;/strong&gt; profile file.</property>
                </serializedBean>
              </formComponent>
            </formComponents>
          </screen>
          <screen id="8" beanClass="com.install4j.runtime.beans.screens.InstallationScreen" rollbackBarrier="true">
            <actions>
              <action id="9" beanClass="com.install4j.runtime.beans.actions.InstallFilesAction" actionElevationType="elevated" failureStrategy="quit" errorMessage="${i18n:FileCorrupted}" />
              <action id="1009" beanClass="com.install4j.runtime.beans.actions.control.SetVariableAction">
                <serializedBean>
                  <property name="responseFileVariable" type="boolean" value="true" />
                  <property name="script">
                    <object class="com.install4j.api.beans.ScriptProperty">
                      <property name="value" type="string">return new java.io.File((String)context.getVariable("sys.installationDir")).getParent();</property>
                    </object>
                  </property>
                  <property name="variableName" type="string">parentDir</property>
                </serializedBean>
              </action>
              <action id="11" beanClass="com.install4j.runtime.beans.actions.desktop.RegisterAddRemoveAction" actionElevationType="elevated">
                <serializedBean>
                  <property name="itemName" type="string">${compiler:sys.fullName} ${compiler:sys.version}</property>
                </serializedBean>
                <condition>!context.getBooleanVariable("npmMode")</condition>
              </action>
              <action id="80" beanClass="com.install4j.runtime.beans.actions.misc.ModifyEnvironmentVariableAction" actionElevationType="elevated">
                <serializedBean>
                  <property name="type" type="enum" class="com.install4j.runtime.beans.actions.misc.ModifyStringType" value="PREPEND" />
                  <property name="value" type="string">${installer:parentDir}</property>
                  <property name="variableName" type="string">Path</property>
                </serializedBean>
                <condition>Util.isWindows() &amp;&amp; (Integer)context.getVariable("addToPath") == 0</condition>
              </action>
              <action id="1010" beanClass="com.install4j.runtime.beans.actions.files.CreateSymlinkAction" actionElevationType="elevated">
                <serializedBean>
                  <property name="file">
                    <object class="java.io.File">
                      <string>${installer:sys.installationDir}/sencha</string>
                    </object>
                  </property>
                  <property name="linkFile">
                    <object class="java.io.File">
                      <string>${installer:parentDir}/sencha-${installer:sys.version}</string>
                    </object>
                  </property>
                </serializedBean>
                <condition>!context.getBooleanVariable("npmMode")</condition>
              </action>
              <action id="484" beanClass="com.install4j.runtime.beans.actions.files.SetModeAction" actionElevationType="elevated">
                <serializedBean>
                  <property name="files" type="array" class="java.io.File" length="1">
                    <element index="0">
                      <object class="java.io.File">
                        <string>${installer:parentDir}/sencha-${installer:sys.version}</string>
                      </object>
                    </element>
                  </property>
                  <property name="mode" type="string">755</property>
                </serializedBean>
                <condition>!Util.isWindows() &amp;&amp; !context.getBooleanVariable("npmMode")</condition>
              </action>
              <action id="542" beanClass="com.install4j.runtime.beans.actions.text.WriteTextFileAction" actionElevationType="elevated">
                <serializedBean>
                  <property name="append" type="boolean" value="true" />
                  <property name="file">
                    <object class="java.io.File">
                      <string>${installer:sys.userHome}/${installer:profile_file}</string>
                    </object>
                  </property>
                  <property name="text" type="string">\nexport PATH="${installer:parentDir}:$PATH"</property>
                </serializedBean>
                <condition>File profileFile = new File(new File((String)context.getVariable("sys.userHome")), (String)context.getVariable("profile_file"));
                 boolean foundInProfileFile = false;
                 if (profileFile.exists()) {
                     Scanner scanner = new Scanner(profileFile);
                     String ln;
                     while(scanner.hasNextLine()){
                         ln = scanner.nextLine().trim();
                         if (ln.contains("PATH=") &amp;&amp; ln.contains((String)context.getVariable("parentDir"))) {
                             foundInProfileFile = true;
                             break;
                         }
                     }
                 }
                 // The action will be executed if:
                 return
                 // The user chose to add Sencha Cmd to the path (0:yes, 1: no)
                 (Integer)context.getVariable("addToPath") == 0 &amp;&amp;
                 // OS is not windows
                 !Util.isWindows() &amp;&amp;
                 // The path entry to add the parentDir is not already in this profile file:
                 !foundInProfileFile;
                </condition>
              </action>
            </actions>
            <formComponents>
              <formComponent id="1025" beanClass="com.install4j.runtime.beans.formcomponents.ProgressComponent">
                <serializedBean>
                  <property name="initialStatusMessage" type="string">${i18n:WizardPreparing}</property>
                </serializedBean>
              </formComponent>
            </formComponents>
          </screen>
          <screen id="12" beanClass="com.install4j.runtime.beans.screens.FinishedScreen" styleId="1115" finishScreen="true">
            <styleOverrides>
              <styleOverride name="Customize banner image" enabled="true">
                <group id="1116" beanClass="com.install4j.runtime.beans.groups.VerticalFormComponentGroup">
                  <serializedBean>
                    <property name="imageEdgeBackgroundColor">
                      <object class="java.awt.Color">
                        <int>255</int>
                        <int>255</int>
                        <int>255</int>
                        <int>255</int>
                      </object>
                    </property>
                    <property name="imageFile">
                      <object class="com.install4j.api.beans.ExternalFile">
                        <string>./installers/sencha-tools-128.png</string>
                      </object>
                    </property>
                  </serializedBean>
                </group>
              </styleOverride>
            </styleOverrides>
            <formComponents>
              <formComponent id="1028" beanClass="com.install4j.runtime.beans.formcomponents.MultilineLabelComponent" insetBottom="10">
                <serializedBean>
                  <property name="labelText" type="string">${form:finishedMessage}</property>
                </serializedBean>
              </formComponent>
            </formComponents>
          </screen>
        </screens>
      </application>
      <application id="uninstaller" beanClass="com.install4j.runtime.beans.applications.UninstallerApplication" styleId="1109" customIcnsFile="./installers/sencha-tools.icns" customIcoFile="./installers/sencha-tools.ico" launchInNewProcess="false">
        <serializedBean>
          <property name="customMacosExecutableName" type="string">${i18n:UninstallerMenuEntry(${compiler:sys.fullName})}</property>
          <property name="executableName" type="string">Uninstaller</property>
          <property name="useCustomIcon" type="boolean" value="true" />
          <property name="useCustomMacosExecutableName" type="boolean" value="true" />
        </serializedBean>
        <styleOverrides>
          <styleOverride name="Custom watermark" enabled="true">
            <formComponent name="Watermark" id="1113" beanClass="com.install4j.runtime.beans.formcomponents.SeparatorComponent">
              <serializedBean>
                <property name="labelText" type="string">Sencha Cmd ${compiler:sys.version} Uninstaller</property>
              </serializedBean>
            </formComponent>
          </styleOverride>
        </styleOverrides>
        <startup>
          <screen id="14" beanClass="com.install4j.runtime.beans.screens.StartupScreen" rollbackBarrierExitCode="0">
            <actions>
              <action id="20" beanClass="com.install4j.runtime.beans.actions.misc.LoadResponseFileAction" />
              <action id="21" beanClass="com.install4j.runtime.beans.actions.misc.RequireInstallerPrivilegesAction" actionElevationType="none" />
            </actions>
          </screen>
        </startup>
        <screens>
          <screen id="15" beanClass="com.install4j.runtime.beans.screens.UninstallWelcomeScreen" styleId="1115">
            <styleOverrides>
              <styleOverride name="Customize banner image" enabled="true">
                <group id="1116" beanClass="com.install4j.runtime.beans.groups.VerticalFormComponentGroup">
                  <serializedBean>
                    <property name="imageEdgeBackgroundColor">
                      <object class="java.awt.Color">
                        <int>255</int>
                        <int>255</int>
                        <int>255</int>
                        <int>255</int>
                      </object>
                    </property>
                    <property name="imageFile">
                      <object class="com.install4j.api.beans.ExternalFile">
                        <string>./installers/sencha-tools-128.png</string>
                      </object>
                    </property>
                  </serializedBean>
                </group>
              </styleOverride>
            </styleOverrides>
            <formComponents>
              <formComponent id="1036" beanClass="com.install4j.runtime.beans.formcomponents.MultilineLabelComponent" insetBottom="10">
                <serializedBean>
                  <property name="labelText" type="string">${form:welcomeMessage}</property>
                </serializedBean>
                <visibilityScript>!context.isConsole()</visibilityScript>
              </formComponent>
              <formComponent id="1037" beanClass="com.install4j.runtime.beans.formcomponents.ConsoleHandlerFormComponent">
                <serializedBean>
                  <property name="consoleScript">
                    <object class="com.install4j.api.beans.ScriptProperty">
                      <property name="value" type="string">String message = context.getMessage("ConfirmUninstall", context.getApplicationName());
return console.askYesNo(message, true);
</property>
                    </object>
                  </property>
                </serializedBean>
              </formComponent>
            </formComponents>
          </screen>
          <screen id="16" beanClass="com.install4j.runtime.beans.screens.UninstallationScreen">
            <actions>
              <action id="17" beanClass="com.install4j.runtime.beans.actions.UninstallFilesAction" actionElevationType="elevated" />
              <action id="585" beanClass="com.install4j.runtime.beans.actions.files.DeleteFileAction" actionElevationType="elevated">
                <serializedBean>
                  <property name="files" type="array" class="java.io.File" length="1">
                    <element index="0">
                      <object class="java.io.File">
                        <string>${installer:parentDir}/version.properties</string>
                      </object>
                    </element>
                  </property>
                </serializedBean>
              </action>
              <action id="1066" beanClass="com.install4j.runtime.beans.actions.files.DeleteFileAction" actionElevationType="elevated">
                <serializedBean>
                  <property name="files" type="array" class="java.io.File" length="1">
                    <element index="0">
                      <object class="java.io.File">
                        <string>${installer:sys.installationDir}</string>
                      </object>
                    </element>
                  </property>
                  <property name="recursive" type="boolean" value="true" />
                </serializedBean>
              </action>
              <action id="1068" beanClass="com.install4j.runtime.beans.actions.control.RunScriptAction">
                <serializedBean>
                  <property name="script">
                    <object class="com.install4j.api.beans.ScriptProperty">
                      <property name="value" type="string">String execName = "sencha" + (Util.isWindows() ? ".exe" : "");
File wrapper = new File(new File((String)context.getVariable("sys.installationDir")).getParentFile(), execName);

if (wrapper.isFile()) {
    ProcessBuilder pb = new ProcessBuilder(wrapper.getPath(), "switch");
    Process p = pb.start();
    BufferedReader reader = new BufferedReader(new InputStreamReader(p.getInputStream()));
    String line = null;
    while ((line = reader.readLine()) != null) {
        System.out.println(line);
    }
}    

return true;</property>
                    </object>
                  </property>
                </serializedBean>
              </action>
            </actions>
            <formComponents>
              <formComponent id="1070" beanClass="com.install4j.runtime.beans.formcomponents.ProgressComponent">
                <serializedBean>
                  <property name="initialStatusMessage" type="string">${i18n:UninstallerPreparing}</property>
                </serializedBean>
              </formComponent>
            </formComponents>
          </screen>
          <screen id="19" beanClass="com.install4j.runtime.beans.screens.UninstallFailureScreen" finishScreen="true" />
          <screen id="18" beanClass="com.install4j.runtime.beans.screens.UninstallSuccessScreen" styleId="1115" finishScreen="true">
            <styleOverrides>
              <styleOverride name="Customize banner image" enabled="true">
                <group id="1116" beanClass="com.install4j.runtime.beans.groups.VerticalFormComponentGroup">
                  <serializedBean>
                    <property name="imageEdgeBackgroundColor">
                      <object class="java.awt.Color">
                        <int>255</int>
                        <int>255</int>
                        <int>255</int>
                        <int>255</int>
                      </object>
                    </property>
                    <property name="imageFile">
                      <object class="com.install4j.api.beans.ExternalFile">
                        <string>./installers/sencha-tools-128.png</string>
                      </object>
                    </property>
                  </serializedBean>
                </group>
              </styleOverride>
            </styleOverrides>
            <formComponents>
              <formComponent id="1074" beanClass="com.install4j.runtime.beans.formcomponents.MultilineLabelComponent" insetBottom="10">
                <serializedBean>
                  <property name="labelText" type="string">${form:successMessage}</property>
                </serializedBean>
              </formComponent>
            </formComponents>
          </screen>
        </screens>
      </application>
    </applications>
    <styles defaultStyleId="1109">
      <style name="Standard" id="1109" beanClass="com.install4j.runtime.beans.styles.FormStyle">
        <formComponents>
          <formComponent name="Header" id="1110" beanClass="com.install4j.runtime.beans.styles.NestedStyleComponent" insetTop="0" insetBottom="0">
            <serializedBean>
              <property name="styleId" type="string">1122</property>
            </serializedBean>
          </formComponent>
          <group name="Main" id="1111" beanClass="com.install4j.runtime.beans.groups.VerticalFormComponentGroup">
            <beans>
              <formComponent id="1112" beanClass="com.install4j.runtime.beans.styles.ContentComponent" insetTop="10" insetLeft="20" insetBottom="10" insetRight="20" />
              <formComponent name="Watermark" id="1113" beanClass="com.install4j.runtime.beans.formcomponents.SeparatorComponent" insetTop="0" insetLeft="5" insetBottom="0" useExternalParametrization="true" externalParametrizationName="Custom watermark" externalParametrizationMode="include">
                <serializedBean>
                  <property name="enabledTitleText" type="boolean" value="false" />
                  <property name="labelText" type="string">install4j</property>
                </serializedBean>
                <externalParametrizationPropertyNames>
                  <propertyName>labelText</propertyName>
                </externalParametrizationPropertyNames>
              </formComponent>
              <formComponent name="Footer" id="1114" beanClass="com.install4j.runtime.beans.styles.NestedStyleComponent" insetTop="0" insetBottom="0">
                <serializedBean>
                  <property name="styleId" type="string">1126</property>
                </serializedBean>
              </formComponent>
            </beans>
          </group>
        </formComponents>
      </style>
      <style name="Banner" id="1115" beanClass="com.install4j.runtime.beans.styles.FormStyle">
        <formComponents>
          <group id="1116" beanClass="com.install4j.runtime.beans.groups.VerticalFormComponentGroup" useExternalParametrization="true" externalParametrizationName="Customize banner image" externalParametrizationMode="include">
            <serializedBean>
              <property name="backgroundColor">
                <object class="com.install4j.runtime.beans.LightOrDarkColor">
                  <object class="java.awt.Color">
                    <int>255</int>
                    <int>255</int>
                    <int>255</int>
                    <int>255</int>
                  </object>
                  <object class="java.awt.Color">
                    <int>49</int>
                    <int>52</int>
                    <int>53</int>
                    <int>255</int>
                  </object>
                </object>
              </property>
              <property name="borderSides">
                <object class="com.install4j.runtime.beans.formcomponents.BorderSides">
                  <property name="bottom" type="boolean" value="true" />
                </object>
              </property>
              <property name="imageEdgeBackgroundColor">
                <object class="com.install4j.runtime.beans.LightOrDarkColor">
                  <object class="java.awt.Color">
                    <int>25</int>
                    <int>143</int>
                    <int>220</int>
                    <int>255</int>
                  </object>
                  <object class="java.awt.Color">
                    <int>0</int>
                    <int>74</int>
                    <int>151</int>
                    <int>255</int>
                  </object>
                </object>
              </property>
              <property name="imageEdgeBorder" type="boolean" value="true" />
              <property name="imageFile">
                <object class="com.install4j.api.beans.ExternalFile">
                  <string>${compiler:sys.install4jHome}/resource/styles/wizard.png</string>
                </object>
              </property>
              <property name="insets">
                <object class="java.awt.Insets">
                  <int>5</int>
                  <int>10</int>
                  <int>10</int>
                  <int>10</int>
                </object>
              </property>
            </serializedBean>
            <beans>
              <formComponent id="1117" beanClass="com.install4j.runtime.beans.styles.ScreenTitleComponent" insetTop="0">
                <serializedBean>
                  <property name="labelFontSizePercent" type="int" value="130" />
                  <property name="labelFontStyle" type="enum" class="com.install4j.runtime.beans.formcomponents.FontStyle" value="BOLD" />
                  <property name="labelFontType" type="enum" class="com.install4j.runtime.beans.formcomponents.FontType" value="DERIVED" />
                </serializedBean>
              </formComponent>
              <formComponent id="1118" beanClass="com.install4j.runtime.beans.formcomponents.SeparatorComponent" />
              <formComponent id="1119" beanClass="com.install4j.runtime.beans.styles.ContentComponent" insetTop="10" insetBottom="0" />
            </beans>
            <externalParametrizationPropertyNames>
              <propertyName>imageAnchor</propertyName>
              <propertyName>imageEdgeBackgroundColor</propertyName>
              <propertyName>imageFile</propertyName>
            </externalParametrizationPropertyNames>
          </group>
          <formComponent id="1120" beanClass="com.install4j.runtime.beans.styles.NestedStyleComponent" insetBottom="0">
            <serializedBean>
              <property name="styleId" type="string">1126</property>
            </serializedBean>
          </formComponent>
        </formComponents>
      </style>
      <group name="Style components" id="1121" beanClass="com.install4j.runtime.beans.groups.StyleGroup">
        <beans>
          <style name="Standard header" id="1122" beanClass="com.install4j.runtime.beans.styles.FormStyle">
            <serializedBean>
              <property name="fillVertical" type="boolean" value="false" />
              <property name="standalone" type="boolean" value="false" />
              <property name="verticalAnchor" type="enum" class="com.install4j.api.beans.Anchor" value="NORTH" />
            </serializedBean>
            <formComponents>
              <group id="1123" beanClass="com.install4j.runtime.beans.groups.VerticalFormComponentGroup" useExternalParametrization="true" externalParametrizationName="Customize title bar" externalParametrizationMode="include">
                <serializedBean>
                  <property name="backgroundColor">
                    <object class="com.install4j.runtime.beans.LightOrDarkColor">
                      <object class="java.awt.Color">
                        <int>255</int>
                        <int>255</int>
                        <int>255</int>
                        <int>255</int>
                      </object>
                      <object class="java.awt.Color">
                        <int>49</int>
                        <int>52</int>
                        <int>53</int>
                        <int>255</int>
                      </object>
                    </object>
                  </property>
                  <property name="borderSides">
                    <object class="com.install4j.runtime.beans.formcomponents.BorderSides">
                      <property name="bottom" type="boolean" value="true" />
                    </object>
                  </property>
                  <property name="imageAnchor" type="enum" class="com.install4j.api.beans.Anchor" value="NORTHEAST" />
                  <property name="imageEdgeBorderWidth" type="int" value="2" />
                  <property name="imageFile">
                    <object class="com.install4j.api.beans.ExternalFile">
                      <string>icon:${installer:sys.installerApplicationMode}_header.png</string>
                    </object>
                  </property>
                  <property name="imageInsets">
                    <object class="java.awt.Insets">
                      <int>0</int>
                      <int>5</int>
                      <int>1</int>
                      <int>1</int>
                    </object>
                  </property>
                  <property name="insets">
                    <object class="java.awt.Insets">
                      <int>0</int>
                      <int>20</int>
                      <int>0</int>
                      <int>10</int>
                    </object>
                  </property>
                </serializedBean>
                <beans>
                  <formComponent name="Title" id="1124" beanClass="com.install4j.runtime.beans.styles.ScreenTitleComponent">
                    <serializedBean>
                      <property name="labelFontStyle" type="enum" class="com.install4j.runtime.beans.formcomponents.FontStyle" value="BOLD" />
                      <property name="labelFontType" type="enum" class="com.install4j.runtime.beans.formcomponents.FontType" value="DERIVED" />
                    </serializedBean>
                  </formComponent>
                  <formComponent name="Subtitle" id="1125" beanClass="com.install4j.runtime.beans.styles.ScreenTitleComponent" insetLeft="8">
                    <serializedBean>
                      <property name="titleType" type="enum" class="com.install4j.runtime.beans.styles.TitleType" value="SUB_TITLE" />
                    </serializedBean>
                  </formComponent>
                </beans>
                <externalParametrizationPropertyNames>
                  <propertyName>backgroundColor</propertyName>
                  <propertyName>foregroundColor</propertyName>
                  <propertyName>imageAnchor</propertyName>
                  <propertyName>imageFile</propertyName>
                  <propertyName>imageOverlap</propertyName>
                </externalParametrizationPropertyNames>
              </group>
            </formComponents>
          </style>
          <style name="Standard footer" id="1126" beanClass="com.install4j.runtime.beans.styles.FormStyle">
            <serializedBean>
              <property name="fillVertical" type="boolean" value="false" />
              <property name="standalone" type="boolean" value="false" />
              <property name="verticalAnchor" type="enum" class="com.install4j.api.beans.Anchor" value="SOUTH" />
            </serializedBean>
            <formComponents>
              <group id="1127" beanClass="com.install4j.runtime.beans.groups.HorizontalFormComponentGroup">
                <serializedBean>
                  <property name="alignFirstLabel" type="boolean" value="false" />
                  <property name="insets">
                    <object class="java.awt.Insets">
                      <int>3</int>
                      <int>5</int>
                      <int>8</int>
                      <int>5</int>
                    </object>
                  </property>
                </serializedBean>
                <beans>
                  <formComponent id="1128" beanClass="com.install4j.runtime.beans.formcomponents.SpringComponent" />
                  <formComponent name="Back button" id="1129" beanClass="com.install4j.runtime.beans.styles.StandardControlButtonComponent">
                    <serializedBean>
                      <property name="buttonText" type="string">&lt; ${i18n:ButtonBack}</property>
                      <property name="controlButtonType" type="enum" class="com.install4j.api.context.ControlButtonType" value="PREVIOUS" />
                    </serializedBean>
                  </formComponent>
                  <formComponent name="Next button" id="1130" beanClass="com.install4j.runtime.beans.styles.StandardControlButtonComponent">
                    <serializedBean>
                      <property name="buttonText" type="string">${i18n:ButtonNext} &gt;</property>
                      <property name="controlButtonType" type="enum" class="com.install4j.api.context.ControlButtonType" value="NEXT" />
                    </serializedBean>
                  </formComponent>
                  <formComponent name="Cancel button" id="1131" beanClass="com.install4j.runtime.beans.styles.StandardControlButtonComponent" insetLeft="5">
                    <serializedBean>
                      <property name="buttonText" type="string">${i18n:ButtonCancel}</property>
                      <property name="controlButtonType" type="enum" class="com.install4j.api.context.ControlButtonType" value="CANCEL" />
                    </serializedBean>
                  </formComponent>
                </beans>
              </group>
            </formComponents>
          </style>
        </beans>
      </group>
    </styles>
  </installerGui>
  <mediaSets>
    <macosFolder name="Mac OS X" id="48" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-osx" installDir="Sencha/Cmd/${compiler:sys.version}" customInstallBaseDir="~/bin" architecture="amd64">
      <excludedComponents>
        <component id="27" />
        <component id="29" />
        <component id="30" />
      </excludedComponents>
    </macosFolder>
    <macosFolder name="Mac OS X No JRE" id="224" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-osx-no_jre" installDir="Sencha/Cmd/${compiler:sys.version}" customInstallBaseDir="~/bin" architecture="amd64">
      <excludedComponents>
        <component id="27" />
        <component id="29" />
        <component id="30" />
      </excludedComponents>
      <exclude>
        <entry filesetId="318" />
        <entry filesetId="322" />
        <entry filesetId="324" />
      </exclude>
      <jreBundle jreBundleSource="none" includedJre="macos-amd64-17.0.12" />
    </macosFolder>
    <windows name="Win32" id="31" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-${compiler:sys.platform}-32bit" installDir="Sencha/Cmd/${compiler:sys.version}" runPostProcessor="true" postProcessor="ant external-codesign -Dbinaryfile=$EXECUTABLE" customInstallBaseDir="~/bin" architecture="32">
      <excludedComponents>
        <component id="28" />
        <component id="29" />
        <component id="30" />
      </excludedComponents>
      <exclude>
        <entry filesetId="320" />
        <entry filesetId="322" />
        <entry filesetId="324" />
      </exclude>
    </windows>
    <windows name="Win64" id="32" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-windows-64bit" installDir="Sencha/Cmd/${compiler:sys.version}" runPostProcessor="true" postProcessor="ant external-codesign -Dbinaryfile=$EXECUTABLE" customInstallBaseDir="~/bin" architecture="64">
      <excludedComponents>
        <component id="28" />
        <component id="29" />
        <component id="30" />
      </excludedComponents>
      <exclude>
        <entry filesetId="320" />
        <entry filesetId="322" />
        <entry filesetId="324" />
      </exclude>
    </windows>
    <windows name="Win No JRE" id="212" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-${compiler:sys.platform}-no_jre" installDir="Sencha/Cmd/${compiler:sys.version}" runPostProcessor="true" postProcessor="ant external-codesign -Dbinaryfile=$EXECUTABLE" customInstallBaseDir="~/bin" architecture="32">
      <excludedComponents>
        <component id="28" />
        <component id="29" />
        <component id="30" />
      </excludedComponents>
      <exclude>
        <entry filesetId="320" />
        <entry filesetId="322" />
        <entry filesetId="324" />
      </exclude>
      <jreBundle jreBundleSource="none" />
    </windows>
    <unixInstaller name="Linux Installer i386" id="273" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-linux-i386" installDir="Sencha/Cmd/${compiler:sys.version}" customInstallBaseDir="~/bin">
      <excludedComponents>
        <component id="27" />
        <component id="28" />
        <component id="30" />
      </excludedComponents>
      <exclude>
        <entry filesetId="318" />
        <entry filesetId="320" />
        <entry filesetId="324" />
      </exclude>
      <jreBundle jreBundleSource="none" />
    </unixInstaller>
    <linuxRPM name="Linux RPM i386" id="260" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-${compiler:sys.platform}-i386" installDir="/opt/sencha/cmd/${compiler:sys.version}">
      <exclude>
        <entry filesetId="318" />
        <entry filesetId="320" />
        <entry filesetId="324" />
      </exclude>
      <jreBundle jreBundleSource="none" />
      <postInstallScript>/bin/sed -i "s/\${cmd.dir}\/\.\./\/var\/sencha\/cmd/" /opt/sencha/cmd/${compiler:sys.version}/sencha.cfg
/bin/mkdir -p /var/sencha/cmd
/bin/chmod 777 -R /var/sencha/cmd

touch /usr/local/bin/sencha
rm -f /usr/local/bin/sencha
ln -s /opt/sencha/cmd/${compiler:sys.version}/sencha /usr/local/bin/sencha
chmod +x /usr/local/bin/sencha</postInstallScript>
    </linuxRPM>
    <linuxDeb name="Linux Deb i386" id="266" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-${compiler:sys.platform}-i386" installDir="/opt/sencha/cmd/${compiler:sys.version}" maintainerEmail="<EMAIL>" architectureSet="true" architecture="i386">
      <exclude>
        <entry filesetId="318" />
        <entry filesetId="320" />
        <entry filesetId="324" />
      </exclude>
      <jreBundle jreBundleSource="none" />
      <postInstallScript>/bin/sed -i "s/\${cmd.dir}\/\.\./\/var\/sencha\/cmd/" /opt/sencha/cmd/${compiler:sys.version}/sencha.cfg
/bin/mkdir -p /var/sencha/cmd
/bin/chmod 777 -R /var/sencha/cmd

touch /usr/local/bin/sencha
rm -f /usr/local/bin/sencha
ln -s /opt/sencha/cmd/${compiler:sys.version}/sencha /usr/local/bin/sencha
chmod +x /usr/local/bin/sencha</postInstallScript>
    </linuxDeb>
    <unixInstaller name="Linux Installer amd64" id="277" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-linux-amd64" installDir="Sencha/Cmd/${compiler:sys.version}" customInstallBaseDir="~/bin">
      <excludedComponents>
        <component id="27" />
        <component id="28" />
        <component id="29" />
      </excludedComponents>
      <exclude>
        <entry filesetId="318" />
        <entry filesetId="320" />
        <entry filesetId="322" />
      </exclude>
      <jreBundle jreBundleSource="none" />
    </unixInstaller>
    <linuxRPM name="Linux RPM amd64" id="262" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-${compiler:sys.platform}-amd64" installDir="/opt/sencha/cmd/${compiler:sys.version}" arch="amd64">
      <exclude>
        <entry filesetId="318" />
        <entry filesetId="320" />
        <entry filesetId="322" />
      </exclude>
      <jreBundle jreBundleSource="none" />
      <postInstallScript>/bin/sed -i "s/\${cmd.dir}\/\.\./\/var\/sencha\/cmd/" /opt/sencha/cmd/${compiler:sys.version}/sencha.cfg
/bin/mkdir -p /var/sencha/cmd
/bin/chmod 777 -R /var/sencha/cmd

touch /usr/local/bin/sencha
rm -f /usr/local/bin/sencha
ln -s /opt/sencha/cmd/${compiler:sys.version}/sencha /usr/local/bin/sencha
chmod +x /usr/local/bin/sencha</postInstallScript>
    </linuxRPM>
    <linuxDeb name="Linux Deb amd64" id="269" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-${compiler:sys.platform}-amd64" installDir="/opt/sencha/cmd/${compiler:sys.version}" maintainerEmail="<EMAIL>" architectureSet="true" architecture="amd64">
      <exclude>
        <entry filesetId="318" />
        <entry filesetId="320" />
        <entry filesetId="322" />
      </exclude>
      <jreBundle jreBundleSource="none" />
      <postInstallScript>/bin/sed -i "s/\${cmd.dir}\/\.\./\/var\/sencha\/cmd/" /opt/sencha/cmd/${compiler:sys.version}/sencha.cfg
/bin/mkdir -p /var/sencha/cmd
/bin/chmod 777 -R /var/sencha/cmd

touch /usr/local/bin/sencha
rm /usr/local/bin/sencha
ln -s /opt/sencha/cmd/${compiler:sys.version}/sencha /usr/local/bin/sencha
chmod +x /usr/local/bin/sencha</postInstallScript>
    </linuxDeb>
    <macosFolder name="macOS ARM" id="1251" mediaFileName="${compiler:sys.shortName}-${compiler:sys.version}-osx-arm" installDir="Sencha/Cmd/${compiler:sys.version}" customInstallBaseDir="~/bin" architecture="aarch64">
      <excludedComponents>
        <component id="27" />
        <component id="29" />
        <component id="30" />
      </excludedComponents>
    </macosFolder>
  </mediaSets>
  <buildIds buildAll="false">
    <mediaSet refId="48" />
    <mediaSet refId="224" />
    <mediaSet refId="31" />
    <mediaSet refId="32" />
    <mediaSet refId="212" />
    <mediaSet refId="273" />
    <mediaSet refId="260" />
    <mediaSet refId="266" />
    <mediaSet refId="277" />
    <mediaSet refId="262" />
    <mediaSet refId="269" />
    <mediaSet refId="1251" />
  </buildIds>
</install4j>
