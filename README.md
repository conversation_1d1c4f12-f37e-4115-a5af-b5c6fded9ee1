# Sencha Cmd

This is the git repo for Sencha Cmd.

Detailed (customer-facing) documentation is provided in the [sencha-command/docs/guides](sencha-command/docs/guides) folder.

## Modules
- [sencha-command](sencha-command/Readme.md)
    - compiler
    - antlib
    - command dispatch
    - utils
- [sencha-command-compass](sencha-command-compass/Readme.md) extension
    - ruby / compass commands
    - compass ant tasks
    - compass gems
- [sencha-fashion](sencha-fashion/Readme.md) extension
    - fashion sass compiler, runtime, and associated commands / ant tasks
- [sencha-command-service](sencha-command-service/Readme.md) extension
    - sencha management server
- [sencha-command-test](sencha-command-test) extension
    - sencha cmd test runner
    - sencha cmd test console
- [sencha-test-harness](sencha-test-harness)
    - test harness for integration / upgrade test suites
- [cmd-packager](cmd-packager/cmd-packager.plugin.xml)
    - ant extension for packaging applications with [phonegap](cmd-packager/sencha-phonegap.md) and [cordova](cmd-packager/sencha-cordova.md)
- [sencha-licenses](sencha-licenses/sencha-licenses.plugin.xml)
    - ant extension to detect when apps are using GPL versions of frameworks

## Development process

### initial setup<a name="initialSetup"/>

* Fork the repo if you haven't already done so:

        http://github.com/sencha/cmd/fork

* Clone your fork to your local machine by running the following command:

        <NAME_EMAIL>:[your-github-username]/cmd.git

* CD into your newly cloned cmd dir

        cd cmd

* Add the main cmd repo as your "upstream" remote:

        git remote <NAME_EMAIL>:sencha/cmd.git

* Fetch the upstream branches:

        git fetch upstream

### Initialize external modules
 Initialize the external modules, see [External Modules](#externalModules) for details

#### Fashion
[The Fashion repository](https://github.com/extjs/fashion)

* Fork Fashion

        <NAME_EMAIL>:[your-github-username]/fashion.git modules/fashion
        cd modules/fashion
        git remote <NAME_EMAIL>:sencha/fashion.git
        git fetch upstream
        cd ../../

* Update Node and NPM [https://docs.npmjs.com/getting-started/installing-node](https://docs.npmjs.com/getting-started/installing-node)

* Install dependent packages

        # install npm modules
		npm install
        
* Build Fashion (NOTE: requires node version 8 or earlier to build)

        gulp clean;
        gulp build;

#### Doxi
[The Doxi repository](https://github.com/sencha/doxi)

* Fork Doxi

        <NAME_EMAIL>:[your-github-username]/doxi.git modules/doxi
        cd modules/doxi
        git remote <NAME_EMAIL>:sencha/doxi.git
        git fetch upstream
        cd ../../

* Build Doxi

        mvn clean install


### Branching

You will need to repeat that step as new branches are created in upstream.

Create a tracking branch - this creates a local branch named cmd-7.9.x that will track
the upstream/cmd-7.9.x branch.  This branch will serve as the starting point to create
other branches on which you will do the actual work.

    git checkout -b cmd-7.9.x upstream/cmd-7.9.x

**NOTE** At the time of writing this README file, the head branch is named cmd-7.9.x.
Make sure to create a tracking branch on the branch you're actually working on or the HEAD
branch.

### Working on a feature / Fixing a bug
#### Before starting
First make sure you are on your tracking branch.  If you're not sure what branch you are on
just run:

    git branch

To switch to the tracking branch, run:

    git checkout cmd-7.9.x

Update the tracking branch with the latest code from upstream

    git pull upstream cmd-7.9.x

**NOTE** You should *not* make commits to the tracking branch. Create "topic" branches
named by a Jira ticket (see below) or just a meaningful name for the work you are doing.

Create a new branch using the Jira ticket number as the branch name.

    git checkout -b SDKTOOLS-42

This creates a new branch containing all of the commits (and code) from cmd-7.9.x.

#### While developing
Make your changes and constantly test them (more information in the next section (Testing)).

#### Submitting your changes
Once all tests pass commit your changes them. For example, to commit all changed files:

    git commit -a -m 'SDKTOOLS-42: fix ALL the bugs'

for more on git commit see http://gitref.org/basic/#commit

Next push your branch to your github repo:

    git push origin SDKTOOLS-42

You should now be able to see your branch on github at:

    https://github.com/[your-github-username]/cmd/tree/SDKTOOLS-42

To submit your code changes for review submit a pull request. You can initiate a pull
request using the green "compare" button on the github page for the main cmd repo, or
at this url:

    https://github.com/sencha/cmd/compare

Use `cmd-7.8.x` as the base branch, and then click the "compare across forks", select
your fork and your bug fix branch, as the "head fork" and "compare" values.

Add a link to the Jira ticket into the PR description and add a link to the PR into the
comments section of the Jira ticket. Click on the "Submit for review" button to transition
the Jira ticket into "Awaiting review" status.

After submitting your pull request, ping an appropriate subject matter expert for code
review. The reviewer will comment on the pull request diff and add a "+1" comment when
review is complete and the pull request is ready to be merged.

SenchaBot will run some Tests on your PR to make sure that the submitted code doesn't
cause any side-effects on other parts of the project. Make sure to keep an eye on the
PR comments and especially to check that all tests are Successful. If a test fails, make
sure to check the logs on Teamcity to find out if your code is breaking something.

Once your pull request has been reviewed and merged, you can delete your local branch by
running:

    git branch -D SDKTOOLS-42

To delete the branch from the origin (your fork) as well:

    git push origin :SDKTOOLS-42

## Testing
There are two levels of testing in the project

- **Unit testing:** Tests that relate to a single logical functional use case in the project
that can be invoked by some public interface (in most cases). Can span a single method, a
whole class or multiple classes working together to achieve one single logical purpose that
can be verified.

- **Integration testing:** Tests to verify the correct inter-operation of all project
components. Can span integration between two classes, to testing integration with other
products (i.e.: Ext JS / Sencha Touch).

### Creating a Unit test
During the development process you'll need to test the progress of the new feature / bugfix
as well as define a set of tests to guarantee that future changes in the code maintain the
same functionality / fix that you are implementing.

Create a class inside the 'test' directory of the module you're working on. Most of the time
you'll want this class to extend from com.sencha.test.TestBase.

    public class MyTest extends TestBase {

    }

If you need to perform some operations before or after each unit test, create a *setUp* and
*tearDown* method (respectively) and annotate them with @Before / @After:

    public class MyTest extends TestBase {
        @Before
        public void setUp() {
            // code that goes here will be executed before each @Test
        }

        @After
        public void tearDown() {
            // code that goes here will be executed after each @Test
        }
    }

Create a new method in this class that will contain your test code. Annotate it with @Test

    @Test
    public void testSomeNewFeature() {
        // perform your test here, make use of assertions to verify the outcome
    }

To run the test, right-click on the method (or class name, depending on whether you want to
run a single or multiple tests) and select "Create '\[method/class name\]'…"

Remove any Before launch configurations from the newly created test and add the
"external-test-setup" Ant target from the module you're testing.

**NOTE** You can remove all default before launch configurations by selecting the "Defaults"
-> "JUnit" node on the Run/Debug Configurations dialog. Beware that doing so will change
several files in the project at once, which you'll need to revert before committing your
changes.

### Running the integration tests
There are certain features/bug fixes that span through a large number of files or touch
several steps of other classes that may be somehow affected by the new code. To make sure
that everything is still working as expected you can run the following test configurations:

- **AppTest** (com.sencha.integration.AppTest): Will generate a new application, make sure that
all files are correctly generated. It will then refresh and build the app and compare the
output to an expected state.

- **SenchaCmd** core test: Runs all tests inside sencha-command/test (including your newly
 developed unit tests)

- **generateExt500App** (com.sencha.integration.Workspace3Test#generateExtApp): Will generate
a new Ext JS 5.x application and compare the output to an expected state. Will also generate
new views and sub-views.


## Building Sencha Cmd
### PreRequisites
- jdk 1.7
- ant 1.8+

Cmd uses ant to manage it's build process, which will need to be installed and 
available via the system path.


### local.properties
There are a few settings needed for building cmd on the command line.  These
may be set in `"local.properties"`, which will be excluded from source control

    # these are required to download needed artifacts from TeamCity
    teamcity.username=
    teamcity.password=

    # disables test artifact download during integration test execution
    # test.skip.artifact.setup=1

    # disables release notes generation
    skip.releasenotes=1

### Build Commands
To clean and build sencha cmd:

    ant clean build

To build sencha cmd and deploy over the current locally installed version:

    ant build-deploy

To build and deploy all but the sencha-test layer:

    ant -Ddeploy.sencha.test=false build-deploy

To run unit and integration tests:

    ant test
    
To run only the unit tests for the core sencha-command project, run the
following from the [sencha-command](sencha-command) directory:

    ant test

To run a particular unit tests for the core sencha-command project, run the
following from the [sencha-command](sencha-command) directory:

    ant test-single -Djavac.includes="**/VariableRenamerTest.java" -Dtest.includes="**/VariableRenamerTest.java"
    
To run only the integration tests, run the following from the 
`"sencha-test-harness"` directory:

    ant test

To run upgrade test sweep (will take at least an hour):

    ant run-upgrade-tests

To build the installers (requires BitRock Install Builder utility):

    ant create-installers

To test build ,Update build.xml file: add path in build.xml file where executable sencha exists.
Ex:
Before:
   <exec executable="sencha" vmlauncher="${use.shell}">

Replace <cmdFolderpath> with the actual folder path where SenchaCmd is installed.
After:

   <exec executable="<cmdFolderpath>/sencha-command/dist/SenchaCmd/sencha" vmlauncher="${use.shell}">

Also Set executable permissions: 
      sudo chmod 777 <cmdFolderpath>/sencha-command/dist/SenchaCmd/sencha

      ant build-deploy