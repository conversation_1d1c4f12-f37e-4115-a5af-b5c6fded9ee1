<ivy-module version="2.0"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:noNamespaceSchemaLocation=
                   "http://www.jayasoft.org/misc/ivy/samples/ivy.xsd"
                xmlns:m="http://ant.apache.org/ivy/maven">
    
    <info organisation="com.sencha" module="sencha-command-test"/>

    <configurations>
        <conf name="compile" description="Required to compile application"/>
        <conf name="runtime" description="Additional run-time dependencies" extends="compile"/>
        <conf name="test"    description="Required for test only" extends="runtime"/>
    </configurations>

    <dependencies>
        <dependency org="org.slf4j" name="slf4j-api" rev="1.6.6" conf="compile->default"/>
        <dependency org="org.apache.ant" name="ant" rev="1.8.4" conf="compile->default"/>
        <dependency org="org.eclipse.jetty" name="jetty-server" rev="12.0.15" conf="runtime->default"/>
        <dependency org="org.eclipse.jetty.ee10" name="jetty-ee10-webapp" rev="12.0.15" conf="runtime->default"/>
        <dependency org="org.eclipse.jetty" name="jetty-proxy" rev="12.0.15" conf="runtime->default"/>
        <dependency org="org.eclipse.jetty" name="jetty-client" rev="12.0.15" conf="runtime->default"/>
        <dependency org="org.eclipse.jetty.ee10" name="jetty-ee10-servlet" rev="12.0.15" conf="runtime->default"/>
    </dependencies>
</ivy-module>