annotation.processing.enabled=true
annotation.processing.enabled.in.editor=false
annotation.processing.processors.list=
annotation.processing.run.all.processors=true
annotation.processing.source.output=${build.generated.sources.dir}/ap-source-output
application.title=sencha-test-harness
application.vendor=kkrohe
build.classes.dir=${build.dir}/classes
build.classes.excludes=**/*.java,**/*.form
# This directory is removed when the project is cleaned:
build.dir=build
build.generated.dir=${build.dir}/generated
build.generated.sources.dir=${build.dir}/generated-sources
# Only compile against the classpath explicitly listed here:
build.sysclasspath=ignore
build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results
# Uncomment to specify the preferred debugger connection transport:
#debug.transport=dt_socket
debug.classpath=${run.classpath}
debug.test.classpath=${run.test.classpath}
# Files in build.classes.dir which should be excluded from distribution jar
dist.archive.excludes=
# This directory is removed when the project is cleaned:
dist.dir=dist
dist.jar=${dist.dir}/sencha-test-harness.jar
dist.javadoc.dir=${dist.dir}/javadoc
endorsed.classpath=
excludes=
includes=**
jar.archive.disabled=${jnlp.enabled}
jar.compress=false
jar.index=${jnlp.enabled}
javac.classpath=${ivy.classpath}\:${reference.sencha-command-compass.jar}\:${reference.sencha-command-service.jar}\:${reference.sencha-command-test.jar}\:${reference.sencha-command.jar}\:${reference.sencha-fashion.jar}
# Space-separated list of extra javac options
javac.compilerargs=
javac.deprecation=false
javac.processorpath=${javac.classpath}
javac.source=1.8
javac.target=1.8
javac.test.classpath=${javac.classpath}\:${build.classes.dir}
javac.test.processorpath=${javac.test.classpath}
javadoc.additionalparam=
javadoc.author=false
javadoc.encoding=${source.encoding}
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.private=false
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
jnlp.codebase.type=no.codebase
jnlp.descriptor=application
jnlp.enabled=false
jnlp.mixed.code=default
jnlp.offline-allowed=false
jnlp.signed=false
jnlp.signing=
jnlp.signing.alias=
jnlp.signing.keystore=
main.class=com.sencha.integration.TestHarnessCommands
# Optional override of default Codebase manifest attribute, use to prevent RIAs from being repurposed
manifest.custom.codebase=
# Optional override of default Permissions manifest attribute (supported values: sandbox, all-permissions)
manifest.custom.permissions=
manifest.file=manifest.mf
meta.inf.dir=${src.dir}/META-INF
mkdist.disabled=false
platform.active=default_platform
project.sencha-command=../sencha-command
project.sencha-command-compass=../sencha-command-compass
project.sencha-command-service=../sencha-command-service
project.sencha-command-test=../sencha-command-test
project.sencha-fashion=../sencha-fashion
reference.sencha-command-compass.jar=${project.sencha-command-compass}/dist/sencha-compass.jar
reference.sencha-command-service.jar=${project.sencha-command-service}/dist/sencha-service.jar
reference.sencha-command-test.jar=${project.sencha-command-test}/dist/sencha-test.jar
reference.sencha-command.jar=${project.sencha-command}/dist/SenchaCmd/sencha.jar
reference.sencha-fashion.jar=${project.sencha-fashion}/dist/sencha-fashion.jar
run.classpath=${javac.classpath}\:${build.classes.dir}
# Space-separated list of JVM arguments used when running the project.
# You may also define separate properties like run-sys-prop.name=value instead of -Dname=value.
# To set system properties for unit tests define test-sys-prop.name=value:
run.test.classpath=${dist.jar}\:${build.test.classes.dir}
source.encoding=UTF-8
src.dir=src
test.src.dir=test
test.run.args=-Xms256m -Xmx3071m
run.jvmargs=-Xms256m -Xmx1024m
junit.forkmode=once
libs.CopyLibs.classpath=../lib/org-netbeans-modules-java-j2seproject-copylibstask.jar
work.dir=${build.dir}/test-temp

ivy.classpath=lib/aether-api-0.9.0.M4.jar\:lib/aether-connector-basic-0.9.0.M4.jar\:lib/aether-impl-0.9.0.M4.jar\:lib/aether-spi-0.9.0.M4.jar\:lib/aether-transport-file-0.9.0.M4.jar\:lib/aether-transport-http-0.9.0.M4.jar\:lib/aether-transport-wagon-0.9.0.M4.jar\:lib/aether-util-0.9.0.M4.jar\:lib/commons-codec-1.6.jar\:lib/graal-sdk-21.2.0.jar\:lib/hamcrest-core-1.3.jar\:lib/httpclient-4.2.6.jar\:lib/httpcore-4.2.5.jar\:lib/icu4j-69.1.jar\:lib/jcl-over-slf4j-1.6.2.jar\:lib/js-21.2.0.jar\:lib/js-scriptengine-21.2.0.jar\:lib/jsch-0.1.44-1.jar\:lib/junit-4.11.jar\:lib/maven-aether-provider-3.1.1.jar\:lib/maven-model-3.1.1.jar\:lib/maven-model-builder-3.1.1.jar\:lib/maven-repository-metadata-3.1.1.jar\:lib/plexus-component-annotations-1.5.5.jar\:lib/plexus-interactivity-api-1.0-alpha-6.jar\:lib/plexus-interpolation-1.19.jar\:lib/plexus-utils-3.0.15.jar\:lib/regex-21.2.0.jar\:lib/slf4j-api-1.6.6.jar\:lib/truffle-api-21.2.0.jar\:lib/wagon-provider-api-1.0.jar\:lib/wagon-ssh-1.0.jar\:lib/wagon-ssh-common-1.0.jar
