package com.sencha.tools.test.agent.selenium;

import com.sencha.util.http.DispatchServlet;
import com.sencha.util.http.Responder;
import jakarta.servlet.ServletException;

public class DispatchServletWrapper extends DispatchServlet {

    @Override
    public void init() throws ServletException {
        Responder responder = (Responder) getServletContext().getAttribute("root.responder");
        setRootResponder(responder);
    }
    
}
