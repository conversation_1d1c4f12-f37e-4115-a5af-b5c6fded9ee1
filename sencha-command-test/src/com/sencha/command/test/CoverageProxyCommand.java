/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.command.test;

import com.sencha.command.BaseSenchaCommand;
import com.sencha.exceptions.ExState;
import com.sencha.logging.SenchaLogManager;
import jakarta.servlet.Servlet;

import org.eclipse.jetty.ee10.servlet.ServletContextHandler;
import org.eclipse.jetty.ee10.servlet.ServletHolder;
import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.server.ServerConnector;
import org.slf4j.Logger;
public class CoverageProxyCommand extends BaseSenchaCommand {
    
    public void setPort(int port) {
        _port = port;
    }
    
    public void setInclude(String include) {
        _includes = include.split(",");
        
    }
    
    public void execute() {
        try {
             // Create and configure the Jetty server
            Server server = new Server();

            ServerConnector connector = new ServerConnector(server);
            connector.setPort(_port);
            server.addConnector(connector);

            // Set up the servlet handler
            Servlet<PERSON>ontextHandler context = new ServletContextHandler(ServletContextHandler.SESSIONS);
            context.setContextPath("/");

            // Set includes for CoverageProxyServlet
            CoverageProxyServlet.setIncludes(_includes);

            // Set up the servlet
            ServletHolder proxyServlet = new ServletHolder();
            proxyServlet.setServlet((Servlet) new CoverageProxyServlet());
            context.addServlet(proxyServlet, "/*");

            // Add the servlet handler to the server
            server.setHandler(context);

            // Log and start the server
            _logger.info("Coverage proxy started");
            server.start();
            server.join();  // Add join to wait for server shutdown
        } catch (Exception e) {
            throw new ExState(e);
        }
    }

    private static final Logger _logger = SenchaLogManager.getLogger();
    private int _port = 8888;
    private String[] _includes = { "src/" };
    
}

