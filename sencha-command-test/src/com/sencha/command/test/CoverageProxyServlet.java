package com.sencha.command.test;

import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.test.coverage.JsCoverageInstrumenter;
import org.eclipse.jetty.client.HttpClient;
import org.eclipse.jetty.client.Request;
import org.eclipse.jetty.client.Response;
import org.eclipse.jetty.http.HttpHeader;
import org.eclipse.jetty.http.HttpMethod;
import org.slf4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.logging.Level;
import javax.servlet.ServletConfig;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.eclipse.jetty.client.InputStreamRequestContent;
import org.eclipse.jetty.http.HttpFields.Mutable;
import org.eclipse.jetty.util.BufferUtil;
import org.eclipse.jetty.util.IO;

public class CoverageProxyServlet extends HttpServlet {

    private static final Logger _logger = SenchaLogManager.getLogger();
    private JsCoverageInstrumenter instrumenter = new JsCoverageInstrumenter();
    private static String[] _includes;
    private String hostHeader;
    HashSet<String> _DontProxyHeaders = new HashSet();

    private HttpClient httpClient;
    private ExecutorService executorService;

    @Override
    public void init(ServletConfig config) {

        this._DontProxyHeaders.add("proxy-connection");
        this._DontProxyHeaders.add("connection");
        this._DontProxyHeaders.add("keep-alive");
        this._DontProxyHeaders.add("transfer-encoding");
        this._DontProxyHeaders.add("te");
        this._DontProxyHeaders.add("trailer");
        this._DontProxyHeaders.add("proxy-authorization");
        this._DontProxyHeaders.add("proxy-authenticate");
        this._DontProxyHeaders.add("upgrade");
        hostHeader = config.getInitParameter("HostHeader");

        executorService = Executors.newFixedThreadPool(10);
        httpClient = new HttpClient();
        httpClient.setExecutor(executorService);
        try {
            httpClient.start();
        } catch (Exception e) {
            throw new RuntimeException("Failed to start HttpClient", e);
        }
    }

    @Override
    protected void service(HttpServletRequest req, HttpServletResponse res) throws IOException {
        String uri = req.getRequestURI();

        if ("CONNECT".equalsIgnoreCase(req.getMethod())) {
            this.handleConnect(req, res);
        } else {
            if (req.getQueryString() != null) {
                uri = uri + "?" + req.getQueryString();
            }
            final String file = uri;
            boolean shouldInstrument = uri.endsWith(".js")
                    && Arrays.stream(_includes).anyMatch(uri::contains);

            String targetUrl = uri;
            HttpMethod method = HttpMethod.fromString(req.getMethod());

            proxyHttpURI(req.getScheme(), req.getServerName(), req.getServerPort(), uri);
            Request proxyRequest = httpClient.newRequest(targetUrl)
                    .method(method);

            Enumeration<String> headerNames = req.getHeaderNames();

            AtomicBoolean hasContent = new AtomicBoolean(false);
            Consumer<Mutable> headerConsumer = (mutable) -> {
                boolean xForwardedFor = false;
                String connectionHdr = req.getHeader("Connection");
                connectionHdr = Optional.ofNullable(connectionHdr).map(String::toLowerCase)
                        .filter(header -> header.contains("keep-alive") || header.contains("close"))
                        .orElse(null);

                while (headerNames.hasMoreElements()) {
                    String headerName = headerNames.nextElement();
                    String lhdr = headerName.toLowerCase();
                    if (!this._DontProxyHeaders.contains(lhdr) && (connectionHdr == null || !connectionHdr.contains(lhdr)) && (this.hostHeader == null || !"host".equals(lhdr))) {

                        if ("content-type".equals(lhdr)) {
                            hasContent.set(true);
                        } else if ("content-length".equals(lhdr)) {
                            long contentLength = (long) req.getContentLength();
                            if (contentLength > 0L) {
                                hasContent.set(true);
                            }
                        } else if ("x-forwarded-for".equals(lhdr)) {
                            xForwardedFor = true;
                        }

                        String headerValue = req.getHeader(headerName);
                        mutable.put(headerName, headerValue);
                    }

                }
                if (this.hostHeader != null) {
                    mutable.put("Host", this.hostHeader);
                }

                if (!xForwardedFor) {
                    mutable.put("X-Forwarded-For", req.getRemoteAddr());
                    mutable.put("X-Forwarded-Proto", req.getScheme());
                    mutable.put("X-Forwarded-Host", req.getHeader("Host"));
                    mutable.put("X-Forwarded-Server", req.getLocalName());
                }
            };

            proxyRequest.headers(headerConsumer);

            if (hasContent.get()) {
                proxyRequest.body(new InputStreamRequestContent(req.getInputStream()));
            }

            // Send the proxied request
            proxyRequest.send(new Response.Listener() {

                private ByteArrayOutputStream responseBuffer = new ByteArrayOutputStream();

                @Override
                public void onHeaders(Response response) {
                    res.setStatus(response.getStatus());
                    response.getHeaders().forEach(header -> {

                        if (shouldInstrument && "Content-Length".toLowerCase().equals(header.getName().toLowerCase())) {
                            return;
                        }

                        if (!_DontProxyHeaders.contains(header.getName()) || (HttpHeader.CONNECTION.asString().equalsIgnoreCase(header.getName()) && "close".equals(header.getValue()))) {

                            CoverageProxyServlet._logger.info(": (filtered): " + header.getValue());
                            res.setHeader(header.getName(), header.getValue());

                        } else {
                            CoverageProxyServlet._logger.info(header.toString());
                        }

                    });
                }

                @Override
                public void onContent(Response response, ByteBuffer content) {
                    try {
                        byte[] contentBytes = BufferUtil.toArray(content);
                        responseBuffer.write(contentBytes);
                    } catch (IOException e) {
                        CoverageProxyServlet._logger.error("exception in Oncontent"+e);
                    }
                }

                @Override
                public void onComplete(org.eclipse.jetty.client.Result result) {
                    try {
                        if (result.isFailed()) {
                            res.sendError(HttpServletResponse.SC_BAD_GATEWAY, "Proxy error");
                            return;
                        }

                        byte[] responseBody = responseBuffer.toByteArray();
                        String content = new String(responseBody, StandardCharsets.UTF_8);

                        // Instrument JavaScript if needed
                        if (shouldInstrument) {

                            boolean canInstrument = content.length() <= 67107840;

                            if (canInstrument) {
                                CoverageProxyServlet._logger.info("Instrumenting {}", file);
                                String instrumentedContent = CoverageProxyServlet.this.instrumenter.instrument(content, file);
                                res.setContentLength(content.getBytes(StandardCharsets.UTF_8).length);
                                res.getWriter().write(instrumentedContent);
                            } else {
                                res.setContentLength(content.getBytes(StandardCharsets.UTF_8).length);
                                res.getWriter().write(content);
                            }

                        }

                    } catch (Exception e) {
                        CoverageProxyServlet._logger.error("Error proxying request", e);
                        try {
                            res.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Proxy error");
                        } catch (IOException ioException) {
                            CoverageProxyServlet._logger.error("Error sending error response", ioException);
                        }
                    }
                }
            });
        }

    }

    public static void setIncludes(String[] includes) {
        _includes = includes;
    }

    public void handleConnect(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String uri = request.getRequestURI();
        String port = "";
        String host = "";
        int c = uri.indexOf(58);
        if (c >= 0) {
            port = uri.substring(c + 1);
            host = uri.substring(0, c);
            if (host.indexOf(47) > 0) {
                host = host.substring(host.indexOf(47) + 1);
            }
        }

        InetSocketAddress inetAddress = new InetSocketAddress(host, Integer.parseInt(port));
        InputStream in = request.getInputStream();
        Socket socket = new Socket(inetAddress.getAddress(), inetAddress.getPort());
        response.setStatus(200);
        response.setHeader("Connection", "close");
        response.flushBuffer();
        IO.copy(in, socket.getOutputStream());
    }

    private URI proxyHttpURI(String scheme, String serverName, int serverPort, String uri) {

        try {
            return new URI(scheme + "://" + serverName + ":" + serverPort + uri);

        } catch (URISyntaxException ex) {
            java.util.logging.Logger.getLogger(CoverageProxyServlet.class.getName()).log(Level.SEVERE, null, ex);
            _logger.error("URL syntax exception", ex);
        }
        return null;
    }

    @Override
    public void destroy() {
        try {
            httpClient.stop();
            executorService.shutdown();
        } catch (Exception e) {
            _logger.error("Error stopping HttpClient", e);
        }
    }
}
